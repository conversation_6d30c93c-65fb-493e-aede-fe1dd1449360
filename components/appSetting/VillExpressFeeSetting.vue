<template>
  <a-card :bordered="false">
    <span slot="title"><PERSON><PERSON> đơn hàng VILLEXPRESS <a-tag color="#f50">LEGACY</a-tag></span>
    <a-row :gutter="12">
      <a-col :span="12">
        <a-form-item label="Phí ship mặc định Lo<PERSON> tiêu chuẩ<PERSON> ( distance <= 3 Km )">
          <a-input-number v-model="form.DEFAULT_LT_3KM_DELIVERY_FEE" size="large" style="width: 100%" :min="0">
          </a-input-number>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="Phí giao hàng trên 3 km ( distance x fee ) Đơn vị K">
          <a-input-number v-model="form.GT_3KM_DELIVERY_FEE" size="large" style="width: 100%" :min="0"></a-input-number>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="Phí giao hàng trên 6 km ( distance x fee )  Đơn vị K">
          <a-input-number v-model="form.GTE_6KM_DELIVERY_FEE" size="large" style="width: 100%" :min="0"></a-input-number>
        </a-form-item>
      </a-col>
      <a-col :span="12">
        <a-form-item label="Phí dịch vụ">
          <a-input-number v-model="form.SERVICE_FEE" size="large" style="width: 100%" :min="0"></a-input-number>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row>
      <a-col :span="8">
        <p>Thay đổi phí giao hàng theo giờ</p>
      </a-col>
      <a-col>
        <a-switch v-model="form.FEE_AUTO_UPDATE_CONF.IS_ENABLE"></a-switch>
      </a-col>
    </a-row>
    <a-row v-show="form.FEE_AUTO_UPDATE_CONF.IS_ENABLE">
      <a-col :span="8">
        <a-form-item style="width: 50%" label="Thời gian">
          <a-time-picker v-model="time1" format="HH:mm" size="large"></a-time-picker>
        </a-form-item>
      </a-col>

      <a-col :span="8">
        <a-form-item label="Phí ship mặc định Loại tiêu chuẩn ( distance <= 3 Km )">
          <a-input-number v-model="form.FEE_AUTO_UPDATE_CONF.DEFAULT_1" :defaultValue="form.DEFAULT_LT_3KM_DELIVERY_FEE"
            :min="0" size="large"></a-input-number>
        </a-form-item>
      </a-col>

      <a-col :span="8">
        <a-form-item label="Phí giao hàng trên 3 km ( distance x fee )">
          <a-input-number v-model="form.FEE_AUTO_UPDATE_CONF.VAL_1" :defaultValue="form.GT_3KM_DELIVERY_FEE" :min="0"
            size="large"></a-input-number>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row v-show="form.FEE_AUTO_UPDATE_CONF.IS_ENABLE">
      <a-col :span="8">
        <a-form-item label="Thời gian">
          <a-time-picker size="large" format="HH:mm" v-model="time2"></a-time-picker>
        </a-form-item>
      </a-col>

      <a-col :span="8">
        <a-form-item label="Phí ship mặc định Loại tiêu chuẩn ( distance <= 3 Km )">
          <a-input-number v-model="form.FEE_AUTO_UPDATE_CONF.DEFAULT_2" :defaultValue="form.DEFAULT_LT_3KM_DELIVERY_FEE"
            :min="0" size="large"></a-input-number>
        </a-form-item>
      </a-col>

      <a-col :span="8">
        <a-form-item label="Phí giao hàng trên 3 km ( distance x fee )">
          <a-input-number v-model="form.FEE_AUTO_UPDATE_CONF.VAL_2" :defaultValue="form.FEE_AUTO_UPDATE_CONF.VAL_2" :min="0"
            size="large"></a-input-number>
        </a-form-item>
      </a-col>
    </a-row>
    <a-row slot="actions" type="flex" justify="end">
      <a-button size="large" type="primary" @click="onUpdate">Cập nhật</a-button>
    </a-row>
  </a-card>
</template>
<script lang="ts">
import Vue from "vue";
import { mapState, mapActions } from "vuex";
import * as _ from "lodash";
import moment from "moment";

interface IVillExpressSettingFee {
  DEFAULT_LT_3KM_DELIVERY_FEE: number;
  GT_3KM_DELIVERY_FEE: number;
  SERVICE_FEE: number;
  FEE_AUTO_UPDATE_CONF: {
    IS_ENABLE: boolean;
    TIME_1: string;
    VAL_1: number;
    TIME_2: string;
    VAL_2: number;
  };
}
export default Vue.extend({
  name: "VILLEXPRESS_SETTING_FEES_setting",
  computed: {
    ...mapState("appSetting", {
      VILLEXPRESS_SETTING_FEES: "VILLEXPRESS_SETTING_FEES",
    }),
  },
  methods: {
    moment,
    ...mapActions("appSetting", {
      update: "update",
    }),
    onUpdate() {
      this.form.FEE_AUTO_UPDATE_CONF.TIME_1 = this.time1.format("HH:mm");
      this.form.FEE_AUTO_UPDATE_CONF.TIME_2 = this.time2.format("HH:mm");
      this.update({
        key: "VILLEXPRESS_SETTING_FEES",
        value: this.form,
      });
    },
    mapping_VILLEXPRESS_SETTING_FEES(val: IVillExpressSettingFee) {
      if (!_.isNil(val)) {
        Object.assign(this.form, val);
        this.time1 = moment(val.FEE_AUTO_UPDATE_CONF.TIME_1, "HH:mm");
        this.time2 = moment(val.FEE_AUTO_UPDATE_CONF.TIME_2, "HH:mm");
      }
    },
  },
  data() {
    return {
      form: {
        DEFAULT_LT_3KM_DELIVERY_FEE: null as number | null,
        GT_3KM_DELIVERY_FEE: null as number | null,
        SERVICE_FEE: null as number | null,
        GTE_6KM_DELIVERY_FEE: null as number | null,
        FEE_AUTO_UPDATE_CONF: {
          IS_ENABLE: false,
          TIME_1: null as string | null,
          DEFAULT_1: null,
          VAL_1: null,
          TIME_2: null as string | null,
          DEFAULT_2: null,
          VAL_2: null,
        },
      },
      time1: moment("21:00", "HH:mm"),
      time2: moment("00:00", "HH:mm"),
    };
  },
  created() {
    this.mapping_VILLEXPRESS_SETTING_FEES(_.cloneDeep(this.VILLEXPRESS_SETTING_FEES));
  },
  watch: {
    VILLEXPRESS_SETTING_FEES: function (val: IVillExpressSettingFee) {
      this.mapping_VILLEXPRESS_SETTING_FEES(_.cloneDeep(val));
      this.time1 = moment(val.FEE_AUTO_UPDATE_CONF.TIME_1, "HH:mm");
      this.time2 = moment(val.FEE_AUTO_UPDATE_CONF.TIME_2, "HH:mm");
    },
  },
});
</script>