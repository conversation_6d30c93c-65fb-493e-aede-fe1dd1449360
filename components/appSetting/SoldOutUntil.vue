<template>
    <a-card title="THỜI GIAN MỞ LẠI MÓN TỐI ĐA" :bordered="false">
        <a-row :gutter="12">
            <a-col :span="24">
                <a-form-item label="Số ngày cho phép mở lại món tối đa">
                    <a-input-number style="width: 100%" size="large" v-model="form.value"></a-input-number>
                </a-form-item>
            </a-col>
        </a-row>
        <a-row slot="actions" type="flex" justify="end">
            <a-button size="large" type="primary" @click="onUpdate">Cập nhật</a-button>
        </a-row>
    </a-card>
</template>
<script>
import Vue from 'vue';
import { mapState, mapActions } from 'vuex';

const key = 'SOLD_OUT_UNTIL';

export default Vue.extend({
    name: 'SoldOutUntil',
    computed: {
        ...mapState('appSetting', {
            SOLD_OUT_UNTIL: key,
        }),
    },
    methods: {
        ...mapActions('appSetting', {
            update: 'update',
        }),
        onUpdate() {
            this.update({
                key: key,
                value: this.form,
            });
        },
        mappingClientAppVersion(val) {
            if (!_.isNil(val)) {
                this.form = {
                    enable: val.enable,
                    value: val.value,
                };
            }
        },
    },
    data() {
        return {
            form: {
                enable: 0,
                value: 0,
            },
        };
    },
    created() {
        this.mappingClientAppVersion(this.SOLD_OUT_UNTIL);
    },
    watch: {
        SOLD_OUT_UNTIL: function (val) {
            this.mappingClientAppVersion(val);
        },
    },
});
</script>
