<template>
  <a-select :value="this.parseValue()" @change="change">
    <a-select-option v-for="item in collections" :key="item.id" :value="item.code">
      {{ item.name }}
    </a-select-option>
  </a-select>
</template>
<script lang="ts">
import Vue from "vue";
import { mapActions, mapState } from "vuex";

export default Vue.extend({
  name: "SelectCollectionCode",
  props: ["value"],
  computed: {
    ...mapState("collection", {
      collections: (state: any) => state.collections,
    }),
  },
  data() {
    return {
      code: null as string | null,
    };
  },
  methods: {
    ...(mapActions({
      fetchList: "collection/fetchList",
    }) as any),
    change(value: any): void {
      this.code = value;
      console.log("value", value);
      this.$emit("code", value);
    },
    parseValue() {
      if (this.code) {
        let collection = this.collections.find(
          (u: any) => u.code === this.code
        );
        return collection ? collection.code : this.code;
      }
      return undefined;
    },
  },

  created() {
    this.code = this.value;
    this.fetchList();
  },
});
</script>
<style lang="less"></style>
