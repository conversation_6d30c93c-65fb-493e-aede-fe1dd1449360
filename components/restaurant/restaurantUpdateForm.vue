<template>
    <div class="content-card">
        <a-spin
            :spinning="
                updateRestaurantPending ||
                fetchRestaurantDetailsPending ||
                updateRestaurantOntopPending ||
                fetchLocationListPending ||
                reAggregateReviewPending
            "
        >
            <a-form-model
                ref="createRestaurantRef"
                layout="vertical"
                :label-col="{ span: 24 }"
                :wrapper-col="{ span: 24 }"
                :rules="rules"
                :model="form"
            >
                <a-row :gutter="24" v-if="restaurant && restaurant.approval_status != 'approved'">
                    <a-col :span="24">
                        <a-card>
                            <a-alert
                                message="Chú ý"
                                description="Chỉ có thể duyệt nhà hàng khi đã có chủ quán."
                                type="warning"
                                closable
                                show-icon
                            >
                            </a-alert>
                        </a-card>
                    </a-col>
                </a-row>

                <br />
                <a-row :gutter="24">
                    <a-col :span="14">
                        <a-card title="Thông tin chung">
                            <a-form-model-item label="Tên cửa hàng" prop="name">
                                <a-input size="large" v-model="form.name" />
                            </a-form-model-item>
                            <a-form-model-item label="Mã cửa hàng">
                                <a-input size="large" v-model="form.code" />
                            </a-form-model-item>
                            <!--  <a-form-model-item label="Chủ cửa hàng">
                                <RestaurantOwnerFilter v-model="form.users" />
                            </a-form-model-item> -->
                            <a-form-model-item label="Loại" prop="categories">
                                <CategoryTagFilter v-model="form.categories" />
                            </a-form-model-item>
                            <a-form-model-item label="Mô tả">
                                <RichEditor v-model="form.description" />
                            </a-form-model-item>
                            <a-form-model-item label="Thông tin">
                                <RichEditor v-model="form.information" />
                            </a-form-model-item>
                            <!-- <a-form-model-item label="Mở cửa" prop="openTime">
                                <a-input size="large" v-model="form.openTime" />
                            </a-form-model-item>
                            <a-form-model-item label="Đóng cửa" prop="closeTime">
                                <a-input size="large" v-model="form.closeTime" />
                            </a-form-model-item> -->
                        </a-card>
                        <br />
                        <a-card title="Liên lạc">
                            <a-form-model-item label="Phone" prop="phone">
                                <a-input size="large" v-model="form.phone" />
                            </a-form-model-item>
                            <a-form-model-item label="Mobile" prop="mobile">
                                <a-input size="large" v-model="form.mobile" />
                            </a-form-model-item>

                            <a-form-model-item label="Latitude" prop="latitude">
                                <a-input size="large" v-model="form.latitude" />
                            </a-form-model-item>
                            <a-form-model-item label="Longitude" prop="longitude">
                                <a-input size="large" v-model="form.longitude" />
                            </a-form-model-item>
                            <a-row>
                                <a-form-model-item prop="cityId" label="Tỉnh/Thành Phố">
                                    <a-select
                                        size="large"
                                        v-model="form.cityId"
                                        @change="onChangeCity"
                                        show-search
                                        :filter-option="false"
                                        @search="(q) => filterOptionLocation(q, location.ELocation.CITY)"
                                        placeholder="Chọn Tỉnh/Thành Phố"
                                    >
                                        <a-select-option
                                            v-for="city in filteredCities"
                                            :key="city.id"
                                            :value="city.id"
                                            >{{ city.name }}</a-select-option
                                        >
                                    </a-select>
                                </a-form-model-item>
                            </a-row>
                            <a-row>
                                <a-form-model-item prop="districtId" label="Quận / Huyện">
                                    <a-select
                                        size="large"
                                        v-model="form.districtId"
                                        @change="onChangeDistrict"
                                        show-search
                                        :filter-option="false"
                                        @search="(q) => filterOptionLocation(q, location.ELocation.DISTRICT)"
                                    >
                                        <a-select-option
                                            v-for="district in filteredDistricts"
                                            :key="district.id"
                                            :value="district.id"
                                            >{{ district.name }}</a-select-option
                                        >
                                    </a-select>
                                </a-form-model-item>
                            </a-row>

                            <a-row>
                                <a-form-model-item prop="locationId" label="Phường/Xã">
                                    <a-select
                                        size="large"
                                        v-model="form.locationId"
                                        show-search
                                        :filter-option="false"
                                        @change="changeAddress"
                                        @search="(q) => filterOptionLocation(q, location.ELocation.WARD)"
                                    >
                                        <a-select-option
                                            v-for="ward in filteredWards"
                                            :key="ward.id"
                                            :value="ward.id"
                                            >{{ ward.name }}</a-select-option
                                        >
                                    </a-select>
                                </a-form-model-item>
                            </a-row>
                            <a-form-model-item label="Địa chỉ cụ thể" prop="street">
                                <a-input size="large" v-model="form.street" @change="changeAddress" />
                            </a-form-model-item>
                            <a-form-model-item label="Địa chỉ (ghi chú) " prop="address_note">
                                <a-input size="large" v-model="form.address_note" />
                            </a-form-model-item>
                            <a-form-model-item label="Địa chỉ" prop="address">
                                <a-input size="large" v-model="form.address" />
                            </a-form-model-item>
                        </a-card>
                        <br />
                        <a-card title="Giờ hoạt động">
                            <template slot="extra">
                                <a-space>
                                    <span>Thời gian mặc định</span>
                                    <a-time-picker v-model="defaultBusinessHours.openTime" format="HH:mm" />
                                    <span>-</span>
                                    <a-time-picker v-model="defaultBusinessHours.closeTime" format="HH:mm" />
                                    <a-button type="primary" @click="setDefaultBusinessHours">Áp dụng</a-button>
                                </a-space>
                            </template>
                            <a-row :gutter="[12, 12]" v-for="item in form.businessHours" :key="item.day">
                                <a-col :span="4">
                                    {{ parseDayOfWeek(item.day) }}
                                </a-col>
                                <a-col :span="4">
                                    <a-switch
                                        :checked="!!item.is_active"
                                        @change="
                                            (checked) =>
                                                onDayBusinessHourStatusChange(item.day, checked == true ? 1 : 0)
                                        "
                                    >
                                    </a-switch>
                                </a-col>
                                <a-col :span="14">
                                    <a-row>
                                        <a-col :span="20" v-if="item.workTimes.length > 0">
                                            <a-row
                                                :gutter="[12, 12]"
                                                v-for="(workTime, idx) in item.workTimes"
                                                :key="idx"
                                            >
                                                <a-col :span="22">
                                                    <a-input-group>
                                                        <a-row :gutter="[12, 12]">
                                                            <a-col :span="11">
                                                                <a-time-picker
                                                                    :disabled="!item.is_active"
                                                                    v-model="workTime.open_time"
                                                                    style="width: 100%"
                                                                    format="HH:mm"
                                                                />
                                                            </a-col>
                                                            <a-col :span="2">
                                                                <a-row type="flex" justify="center"> - </a-row>
                                                            </a-col>
                                                            <a-col :span="11">
                                                                <a-time-picker
                                                                    :disabled="!item.is_active"
                                                                    v-model="workTime.close_time"
                                                                    style="width: 100%"
                                                                    format="HH:mm"
                                                                />
                                                            </a-col>
                                                        </a-row>
                                                    </a-input-group>
                                                </a-col>
                                                <a-col :span="2">
                                                    <a-button
                                                        v-if="item.workTimes.length > 0"
                                                        type="link"
                                                        icon="close"
                                                        @click="removeWorkTimes(item.day, idx)"
                                                    ></a-button>
                                                </a-col>
                                            </a-row>
                                        </a-col>
                                        <a-col :span="20" v-else>Hoạt động 24h</a-col>
                                        <a-col :span="4">
                                            <a-button type="link" @click="addWorkTimes(item.day)"
                                                >Thêm khung giờ
                                            </a-button>
                                        </a-col>
                                    </a-row>
                                </a-col>
                            </a-row>
                        </a-card>
                    </a-col>
                    <a-col :span="10">
                        <!-- <br v-if="['admin'].some(
                            (role) =>
                                profile.roles.findIndex((o) => o['name'] === role) >= 0
                        )
                            " /> -->
                        <!-- <a-row type="flex" justify="end">
                            <a-button
                                :disabled="updateRestaurantPending || updateRestaurantOntopPending || reAggregateReviewPending"
                                type="primary" size="large" @click="() => aggregareReview(this.$route.params.id)">
                                Tổng hợp lại review
                            </a-button>
                        </a-row> -->
                        <!-- <br /> -->
                        <a-card title="Hình">
                            <a-row type="flex" justify="center">
                                <a-col>
                                    <ImageUploader
                                        :uploadUrl="uploadUrl"
                                        :defaultImg="restaurant ? restaurant.image : null"
                                    />
                                </a-col>
                            </a-row>
                        </a-card>
                        <br />
                        <a-card title="Hình Background">
                            <a-row type="flex" justify="center">
                                <a-col>
                                    <BackgroundUploader
                                        :uploadUrl="uploadBackgroundUrl"
                                        :defaultImg="restaurant ? restaurant.background : null"
                                    />
                                </a-col>
                            </a-row>
                        </a-card>
                        <br />
                        <a-card title="Khung hình">
                            <a-row :gutter="[12, 12]">
                                <a-col :span="24" v-if="form.frame">
                                    <a-row type="flex" justify="center">
                                        <div class="restaurant-img-wrapper">
                                            <img class="main-img" :src="restaurant ? restaurant.image : null" />
                                            <img class="frame-img" :src="form.frame.frame_url" />
                                        </div>
                                    </a-row>
                                </a-col>
                                <a-col :span="24">
                                    <FrameSelect v-model="form.frame" />
                                </a-col>
                            </a-row>
                        </a-card>
                        <br />
                        <a-card title="Thiết lập">
                            <a-form-model-item
                                label="Trạng thái"
                                prop="operatingStatus"
                                :help="
                                    'Tạm thời đóng cửa đến ' +
                                    formatDate(
                                        restaurant && restaurant.operating_status == 'tempt_close'
                                            ? restaurant.reopen_time
                                            : null,
                                    )
                                "
                            >
                                <a-select size="large" v-model="form.operatingStatus">
                                    <a-select-option value="open">Đang mở cửa</a-select-option>
                                    <a-select-option value="tempt_close">Tạm thời đóng cửa</a-select-option>
                                    <a-select-option value="contact_terminated">Ngừng hợp tác</a-select-option>
                                    <a-select-option value="inactive_by_system">Tạm khóa</a-select-option>
                                </a-select>
                            </a-form-model-item>

                            <a-form-model-item v-if="form.operatingStatus === 'tempt_close'" label="Giờ mở lại">
                                <a-date-picker
                                    style="width: 100%"
                                    :show-time="{ defaultValue: moment() }"
                                    :defaultValue="moment()"
                                    format="YYYY-MM-DD HH:mm:ss"
                                    v-model="form.reopenTime"
                                />
                            </a-form-model-item>
                            <!-- <a-alert message="Chú ý" description="Chỉ có thể duyệt nhà hàng khi đã có chủ quán." type="warning" closable show-icon>
                            </a-alert> -->
                            <a-form-model-item label="Trạng thái xét duyệt" prop="approval_status">
                                <a-select size="large" v-model="form.approval_status">
                                    <a-select-option value="approved">Đã duyệt</a-select-option>
                                    <a-select-option value="pending">Đang chờ</a-select-option>
                                    <a-select-option value="denied">Từ chối</a-select-option>
                                </a-select>
                            </a-form-model-item>
                            <a-form-model-item label="Quán thử nghiệm">
                                <a-switch v-model="form.is_trial" />
                            </a-form-model-item>
                            <a-form-model-item label="Nhà hàng FREESHIP">
                                <a-switch v-model="form.freeship" />
                            </a-form-model-item>
                            <a-form-model-item label="Nhà hàng top khuyến mãi">
                                <a-switch v-model="form.top" />
                            </a-form-model-item>
                            <a-form-model-item label="Món độc quán quen">
                                <a-switch v-model="form.mondoc" />
                            </a-form-model-item>
                            <a-form-model-item label="Buổi sáng ăn gì?">
                                <a-switch v-model="form.buoisang" />
                            </a-form-model-item>
                            <a-form-model-item label="Buổi trưa">
                                <a-switch v-model="form.buoitrua" />
                            </a-form-model-item>
                            <a-form-model-item label="Buổi tối">
                                <a-switch v-model="form.buoitoi" />
                            </a-form-model-item>
                            <!-- <a-form-model-item label="On top"
                                  v-if="['admin', 'Giam Đốc Tỉnh', 'Nhân viên kinh doanh', 'Nhân Viên Content', 'Phân Tích Thị Trường'].some((role) => profile.roles.findIndex((o) => o['name'] === role) >= 0)">
                                  <a-switch v-model="form.onTop" />
                              </a-form-model-item> -->
                            <a-form-model-item label="Quán đối tác">
                                <a-switch v-model="form.star" />
                            </a-form-model-item>
                            <a-form-model-item label="Đang hợp tác">
                                <a-switch v-model="form.cooperating" />
                            </a-form-model-item>
                            <a-form-model-item label="Quán đang mở cửa">
                                <a-switch v-model="form.status" />
                            </a-form-model-item>
                            <a-form-model-item label="Quán chuẩn bị món lâu">
                                <a-switch
                                    :checked="Boolean(form.long_preparing)"
                                    @change="(checked) => (form.long_preparing = checked ? 1 : 0)"
                                />
                            </a-form-model-item>
                            <a-form-model-item label="Nằm trong danh sách xu hướng">
                                <a-switch
                                    :checked="!!form.top_trending"
                                    @change="(checked) => (form.top_trending = checked == true ? 1 : 0)"
                                />
                            </a-form-model-item>
                            <a-form-model-item label="Có món ăn buổi xế chiều(13h-16h)">
                                <a-switch
                                    :checked="!!form.mid_afternoon"
                                    @change="(checked) => (form.mid_afternoon = checked == true ? 1 : 0)"
                                />
                            </a-form-model-item>
                            <a-form-model-item label="Quảng cáo trên newsfeed">
                                <a-switch
                                    :checked="!!form.newsfeed_ads"
                                    @change="(checked) => (form.newsfeed_ads = checked == true ? 1 : 0)"
                                />
                            </a-form-model-item>

                            <permission-wrapper :permissions="[$PERMISSIONS.RESTAURANT_UPDATE_ONTOP]">
                                <a-form-model-item label="On top">
                                    <a-switch :checked="!!form.onTop" @change="(checked) => (form.onTop = checked)" />
                                </a-form-model-item>
                            </permission-wrapper>
                        </a-card>
                        <br />

                        <a-card title="Thiết lập khác">
                            <a-form-model-item label="Thuộc chi nhánh" prop="provinceId">
                                <SubProvinceSelect
                                    :value="form.provinceId"
                                    @change="(value) => (form.provinceId = value)"
                                />
                            </a-form-model-item>
                            <a-form-model-item label="Thuộc branch" prop="branchId">
                                <BranchSelector :value="form.branchId" @change="(value) => (form.branchId = value)" />
                            </a-form-model-item>
                        </a-card>
                    </a-col>
                </a-row>
                <br />
                <a-row type="flex" justify="end">
                    <a-button
                        :disabled="updateRestaurantPending || updateRestaurantOntopPending"
                        type="primary"
                        size="large"
                        @click="onSubmit"
                    >
                        <a-icon type="save" /> Save
                    </a-button>
                </a-row>
            </a-form-model>
        </a-spin>
    </div>
</template>
<script>
import Vue from 'vue';
import { mapState, mapActions } from 'vuex';
import * as moment from 'moment';
import * as _ from 'lodash';

import RestaurantOwnerFilter from '@/components/common/RestaurantOwnerFilter.vue';
import CategoryTagFilter from '@/components/common/CategoryTagFilter.vue';
import ShipperTagFilter from '@/components/common/RestaurantShipperTagFilter.vue';
import ImageUploader from '@/components/common/ImageUploader.vue';
import FrameSelect from '@/components/common/FrameSelect.vue';
import RichEditor from '@/components/common/Edittor/index.vue';
import RestaurantMenu from '@/components/restaurant/restaurantMenu.vue';
import BranchSelector from '@/components/branch/branchSelector.vue';
import * as restaurantHelper from '@/utils/restaurant.helper';
import { TinyInt } from '@/types/common.interface';
import { removeUnicode } from '@/utils/common';
import BackgroundUploader from '@/components/restaurant/backgroundUplaoder.vue';

import { restaurants as apis } from '@/constants/apiPaths';
import SubProvinceSelect from '../province/subProvinceSelect.vue';
import { Permissions } from '@/constants/constants';
import { fetchLocationListByParentId } from '@/api/location.api';

export default Vue.extend({
    name: 'RestaurantUpdateForm',
    components: {
        RestaurantOwnerFilter,
        CategoryTagFilter,
        ShipperTagFilter,
        ImageUploader,
        FrameSelect,
        RichEditor,
        RestaurantMenu,
        SubProvinceSelect,
        BackgroundUploader,
        BranchSelector,
    },
    computed: {
        ...mapState('restaurant', {
            restaurant: (state) => state.restaurant,
            fetchRestaurantDetailsPending: (state) => state.fetchRestaurantDetailsPending,
            fetchRestaurantDetailsSuccess: (state) => state.fetchRestaurantDetailsSuccess,
            fetchRestaurantDetailsError: (state) => state.fetchRestaurantDetailsError,
            fetchRestaurantDetailsMsg: (state) => state.fetchRestaurantDetailsMsg,

            updateRestaurantPending: (state) => state.updateRestaurantPending,
            updateRestaurantSuccess: (state) => state.updateRestaurantSuccess,
            updateRestaurantError: (state) => state.updateRestaurantError,
            updateRestaurantMsg: (state) => state.updateRestaurantMsg,

            updateRestaurantOntopPending: (state) => state.updateRestaurantOntopPending,
            updateRestaurantOntopError: (state) => state.updateRestaurantOntopError,
            updateRestaurantOntopSuccess: (state) => state.updateRestaurantOntopSuccess,
            updateRestaurantOntopMsg: (state) => state.updateRestaurantOntopMsg,

            reAggregateReviewPending: (state) => state.reAggregateReviewPending,
            reAggregateReviewError: (state) => state.reAggregateReviewError,
            reAggregateReviewSuccess: (state) => state.reAggregateReviewSuccess,
            reAggregateReviewMsg: (state) => state.reAggregateReviewMsg,
        }),
        ...mapState('user', {
            profile: (state) => state.profile,
        }),
        ...mapState('location', {
            locationList: (state) => state.locationList,
            fetchLocationListPending: (state) => state.fetchLocationListPending,
            fetchLocationListSuccess: (state) => state.fetchLocationListSuccess,
            fetchLocationListError: (state) => state.fetchLocationListError,
            fetchLocationListMsg: (state) => state.fetchLocationListMsg,

            childLocationList: (state) => state.childLocationList,
            fetchLocationListByParentIdSuccess: (state) => state.fetchLocationListByParentIdSuccess,
            fetchLocationListByParentIdPending: (state) => state.fetchLocationListByParentIdPending,
            fetchLocationListByParentIdError: (state) => state.fetchLocationListByParentIdError,
            fetchLocationListByParentIdMsg: (state) => {
                state.fetchLocationListByParentIdMsg;
            },
        }),
        uploadUrl() {
            return apis.UPLOAD_IMAGE.replace(':id', this.restaurant?.id);
        },
        uploadBackgroundUrl() {
            return apis.UPLOAD_BACKGROUND.replace(':id', this.restaurant?.id);
        },
        filteredCities() {
            const cities = JSON.parse(JSON.stringify(this.location.cities));
            return cities.filter((city) => removeUnicode(city.name.toLowerCase()).includes(this.location.queryCity));
        },
        filteredDistricts() {
            const districts = JSON.parse(JSON.stringify(this.location.districts));
            return districts.filter((district) =>
                removeUnicode(district.name.toLowerCase()).includes(this.location.queryDistrict),
            );
        },
        filteredWards() {
            const wards = JSON.parse(JSON.stringify(this.location.wards));
            return wards.filter((ward) => removeUnicode(ward.name.toLowerCase()).includes(this.location.queryWard));
        },
    },
    data() {
        return {
            location: {
                districts: [],
                wards: [],
                cities: [],
                queryCity: '',
                queryDistrict: '',
                queryWard: '',
                ELocation: {
                    CITY: 'CITY',
                    DISTRICT: 'DISTRICT',
                    WARD: 'WARD',
                },
            },
            defaultBusinessHours: {
                openTime: null,
                closeTime: null,
            },
            form: {
                name: undefined,
                address: undefined,
                street: undefined,
                locationId: '',
                districtId: '',
                cityId: '',
                /*    users: [], */
                categories: [],
                deliveryFee: 0,
                phone: '',
                mobile: '',
                latitude: '',
                longitude: '',
                /*  openTime: '',
                closeTime: '', */
                description: '',
                information: '',
                tradeDiscountType: 0,
                tradeDiscount: 0,
                tradeDiscountPeriodType: null,
                star: false,
                freeship: false,
                top: false,
                mondoc: false,
                buoisang: false,
                buoitoi: false,
                buoitrua: false,
                is_trial: false,
                cooperating: false,
                onTop: false,
                couponType: 'fixed',
                coupon: null,
                couponOrder: null,
                couponValue: null,
                couponExp: null,
                status: false,
                couponTime: null,
                top_trending: false,
                businessHours: [],
                operatingStatus: undefined,
                code: undefined,
                frame: undefined,
                approval_status: undefined,
                long_preparing: TinyInt.NO,
                mid_afternoon: false,
                newsfeed_ads: false,
                reopenTime: null,
                branchId: null,
                address_note: '',
            },
            rules: {
                name: [
                    {
                        required: true,
                        message: 'Please input Restaurant name',
                        trigger: 'blur',
                    },
                ],
                description: [
                    {
                        required: true,
                        message: 'Please input description',
                        trigger: 'blur',
                    },
                ],
                information: [
                    {
                        required: true,
                        message: 'Please input information',
                        trigger: 'blur',
                    },
                ],
                categories: [
                    {
                        required: true,
                        message: 'Please select Restaurant categories',
                        trigger: 'blur',
                    },
                ],
                address: [{ required: true, message: 'Please input address', trigger: 'blur' }],
                // cityId: [
                //     { required: true, message: "Please input city", trigger: "blur" },
                // ],
                // districtId: [
                //     {
                //         required: true,
                //         message: "Please input district",
                //         trigger: "blur",
                //     },
                // ],
                // locationId: [
                //     { required: true, message: "Please input ward", trigger: "blur" },
                // ],
                // street: [
                //     { required: true, message: "Please input street", trigger: "blur" },
                // ],
                phone: [{ required: true, message: 'Please input phone', trigger: 'blur' }],
                mobile: [{ required: true, message: 'Please input mobile', trigger: 'blur' }],
                latitude: [
                    {
                        required: true,
                        validator: restaurantHelper.validateLatidude,
                        trigger: 'blur',
                    },
                ],
                longitude: [
                    {
                        required: true,
                        validator: restaurantHelper.validateLongitude,
                        trigger: 'blur',
                    },
                ],
                /*   closeTime: [
                    {
                        required: true,
                        message: 'Please input closeTime',
                        trigger: 'blur',
                    },
                ], */
                /*    openTime: [{ required: true, message: 'Please input openTime', trigger: 'blur' }], */
                tradeDiscountType: [
                    {
                        required: true,
                        message: 'Please select trade discount type',
                        trigger: 'blur',
                    },
                ],
                tradeDiscount: [
                    {
                        required: true,
                        message: 'Please input trade discount',
                        trigger: 'blur',
                        min: 0,
                        type: 'number',
                    },
                ],
                tradeDiscountPeriodType: [
                    {
                        required: true,
                        message: 'Please select trade discount period type',
                        trigger: 'blur',
                    },
                ],
                operatingStatus: [{ required: true, message: 'Chọn trạng thái', trigger: 'blur' }],
                code: [
                    {
                        required: true,
                        message: 'Yêu cầu nhập mã nhà hàng',
                        trigger: 'blur',
                    },
                ],
                approval_status: [
                    {
                        required: true,
                        message: 'Chọn trạng thái xét duyệt',
                        trigger: 'blur',
                    },
                ],
                provinceId: [{ required: true, message: 'Chọn chi nhánh', trigger: 'blur' }],
                street: [{ required: true, message: 'Please input address', trigger: 'blur' }],
            },
        };
    },
    methods: {
        moment,
        ...mapActions({
            fetchRestaurantDetails: 'restaurant/fetchRestaurantDetails',
            updateRestaurant: 'restaurant/updateRestaurant',
            updateRestaurantOntop: 'restaurant/updateRestaurantOntop',
            reopenRestaurantJob: 'restaurant/reopenRestaurantJob',
            fetchLocationList: 'location/fetchLocationList',
            fetchLocationListByParentId: 'location/fetchLocationListByParentId',
            aggregareReview: 'restaurant/reAggregateTotalReviewsAndRating',
        }),
        onSubmit() {
            this.$refs.createRestaurantRef.validate((valid) => {
                if (valid) {
                    let {
                        couponExp,
                        // users,
                        categories,
                        businessHours,
                        frame,
                        latitude,
                        longitude,
                        reopenTime,
                        onTop,
                        locationId,
                        street,
                    } = this.form;
                    let params = {
                        ...this.form,
                        location: {
                            street,
                            locationId,
                        },
                        latitude: latitude.trim(),
                        longitude: longitude.trim(),
                        frameId: frame ? frame.id : null,
                        couponType: _.isUndefined(this.form.couponType) ? null : this.form.couponType,
                        couponExp: couponExp ? couponExp.toISOString() : null,
                        // users: _.map(users, 'id'),
                        categories: _.map(categories, 'id'),
                        businessHours: businessHours.map((businessHour) => {
                            return {
                                ...businessHour,
                                workTimes: businessHour.workTimes.map(({ id, open_time, close_time }) => {
                                    return {
                                        id,
                                        open_time: open_time.format('HH:mm:ss'),
                                        close_time: close_time.format('HH:mm:ss'),
                                    };
                                }),
                            };
                        }),
                        reopenTime,
                        address_note: this.form.address_note,
                    };
                    if (this.restaurant) {
                        if (!params.description) {
                            console.log('13123123');
                            return this.$notification.error({
                                message: 'Description không được trống',
                                description: 'Description không được trống',
                            });
                        }

                        this.updateRestaurant({
                            id: this.restaurant.id,
                            params: _.omitBy(params, _.isUndefined),
                        });
                        if (
                            this.profile['permissions'] &&
                            this.profile.permissions.includes(Permissions.RESTAURANT_UPDATE_ONTOP)
                        ) {
                            this.updateRestaurantOntop({
                                id: this.restaurant.id,
                                params: { onTop },
                            });
                        }
                    } else {
                        return false;
                    }
                }
            });
        },

        parseDayOfWeek(day) {
            return moment().day(day).format('dddd');
        },
        onDayBusinessHourStatusChange(day, isActive) {
            let index = this.form.businessHours.findIndex((item) => item.day == day);
            if (index >= 0) this.form.businessHours[index].is_active = isActive;
        },
        addWorkTimes(day) {
            let index = this.form.businessHours.findIndex((item) => item.day == day);
            if (index >= 0)
                this.form.businessHours[index].workTimes.push({
                    open_time: moment('08:00', 'HH:mm'),
                    close_time: moment('17:00', 'HH:mm'),
                });
        },
        removeWorkTimes(day, workTimeIndex) {
            let index = this.form.businessHours.findIndex((item) => item.day == day);
            if (index >= 0) {
                if (workTimeIndex >= 0) {
                    this.form.businessHours[index].workTimes.splice(workTimeIndex, 1);
                }
            }
        },
        formatDate(date) {
            return date ? moment(date).format('YYYY-MM-DD HH:mm:ss') : '';
        },
        setDefaultBusinessHours() {
            const { openTime, closeTime } = this.defaultBusinessHours;
            if (openTime && closeTime) {
                this.form.businessHours.forEach(({ workTimes }, index) => {
                    this.form.businessHours[index].workTimes = workTimes.map(({ id }) => {
                        return {
                            id,
                            open_time: moment(openTime),
                            close_time: moment(closeTime),
                        };
                    });
                });
            }
        },

        reopenJob() {
            if (this.form.reopenTime && this.form.operatingStatus == 'tempt_close') {
                this.reopenRestaurantJob({
                    restaurantId: this.restaurant.id,
                    provinceId: this.restaurant.province_id,
                });
            } else {
                this.form.reopenTime = null;
            }
        },

        changeAddress() {
            const city = this.location.cities.find((city) => city.id === this.form.cityId);
            const district = this.location.districts.find((district) => district.id === this.form.districtId);
            const ward = this.location.wards.find((ward) => ward.id === this.form.locationId);
            const cityName = city?.name || '';
            const districtName = district?.name ? district?.name + ', ' : '';
            const wardName = ward?.name ? ward?.name + ', ' : '';
            const street = this.form.street ? this.form.street + ', ' : '';
            this.form.address = `${street}${wardName}${districtName}${cityName}`;
        },

        async onChangeCity() {
            // this.fetchLocationListByParentId(this.form.cityId);
            const districtsData = await fetchLocationListByParentId(this.form.cityId);
            this.location.districts = districtsData?.data?.data || [];
            this.form.districtId = '';
            this.form.locationId = '';
            this.location.queryCity = '';
            this.location.queryDistrict = '';
            this.location.queryWard = '';
            this.changeAddress();
        },
        async onChangeDistrict() {
            // this.fetchLocationListByParentId(this.form.districtId);
            const wardsData = await fetchLocationListByParentId(this.form.districtId);
            this.location.wards = wardsData?.data?.data || [];
            this.form.locationId = '';
            this.location.queryCity = '';
            this.location.queryDistrict = '';
            this.location.queryWard = '';
            this.changeAddress();
        },
        filterOptionLocation(input, name) {
            switch (name) {
                case this.location.ELocation.CITY:
                    this.location.queryCity = input;
                    break;
                case this.location.ELocation.DISTRICT:
                    this.location.queryDistrict = input;
                    break;
                case this.location.ELocation.WARD:
                    this.location.queryWard = input;
                    break;
                default:
                    break;
            }
        },
    },

    created() {
        this.fetchRestaurantDetails(this.$route.params.id);
        this.fetchLocationList();
    },
    watch: {
        restaurant: async function (val, oldVal) {
            if (val) {
                const cityId = val?.location?.location?.district?.city?.id || '';
                const districtId = val?.location?.location?.district?.id || '';
                let {
                    name,
                    address,
                    location,
                    // users,
                    categories,
                    phone,
                    mobile,
                    latitude,
                    longitude,
                    /*   time_close,
                    time_open, */
                    delivery_fee,
                    trade_discount,
                    trade_discount_type,
                    description,
                    information,
                    coupon_order,
                    top,
                    on_top,
                    coupon,
                    star,
                    coupon_type,
                    cooperating,
                    freeship,
                    mondoc,
                    buoitrua,
                    buoitoi,
                    buoisang,
                    coupon_value,
                    coupon_exp,
                    coupon_time,
                    status,
                    top_trending,
                    trade_discount_period_type,
                    businessHours,
                    operating_status,
                    code,
                    frame,
                    approval_status,
                    reopen_time,
                    long_preparing,
                    mid_afternoon,
                    province_id,
                    newsfeed_ads,
                    is_trial,
                    branch_id,
                    address_note,
                } = val;
                this.form = {
                    name: name,
                    address: address,
                    street: location?.street || '',
                    cityId,
                    districtId,
                    locationId: location?.location_id || '',
                    // users: users,
                    categories,
                    phone,
                    mobile,
                    latitude,
                    longitude,
                    /*  openTime: time_open,
                    closeTime: time_close, */
                    deliveryFee: delivery_fee,
                    tradeDiscountType: trade_discount_type,
                    tradeDiscount: trade_discount,
                    tradeDiscountPeriodType: trade_discount_period_type,
                    description,
                    information,
                    top,
                    star,
                    onTop: on_top,
                    coupon,
                    cooperating,
                    buoitoi,
                    buoitrua,
                    buoisang,
                    freeship,
                    mondoc,
                    couponType: coupon_type,
                    couponOrder: coupon_order,
                    couponValue: coupon_value,
                    couponExp: coupon_exp ? moment(coupon_exp) : null,
                    couponTime: coupon_time,
                    status,
                    top_trending,
                    businessHours: businessHours.map((item) => {
                        return {
                            ...item,
                            workTimes: item.workTimes.map(({ id, open_time, close_time }) => {
                                return {
                                    id,
                                    open_time: moment(open_time, 'HH:mm'),
                                    close_time: moment(close_time, 'HH:mm'),
                                };
                            }),
                        };
                    }),
                    operatingStatus: operating_status,
                    code,
                    frame,
                    approval_status,
                    reopenTime: reopen_time ? moment(reopen_time) : null,
                    long_preparing,
                    mid_afternoon,
                    provinceId: province_id,
                    newsfeed_ads,
                    is_trial,
                    branchId: branch_id,
                    address_note,
                };
                if (cityId && districtId) {
                    // this.fetchLocationListByParentId(cityId);
                    // this.fetchLocationListByParentId(districtId);
                    const districtsData = await fetchLocationListByParentId(this.form.cityId);
                    this.location.districts = districtsData?.data?.data || [];
                    const wardsData = await fetchLocationListByParentId(this.form.districtId);
                    this.location.wards = wardsData?.data?.data || [];
                }
            }
        },
        updateRestaurantSuccess: function (val, oldVal) {
            if (val && val !== oldVal) {
                this.$notification.success({
                    message: 'CẬP NHẬT THÔNG TIN CỬA HÀNG THÀNH CÔNG',
                    description: 'CẬP NHẬT THÔNG TIN CỬA HÀNG THÀNH CÔNG',
                });
            }
        },
        updateRestaurantError: function (val, oldVal) {
            if (val && val !== oldVal) {
                this.$notification.error({
                    message: 'CẬP NHẬT THÔNG TIN CỬA HÀNG THẤT BẠI',
                    description: this.updateRestaurantMsg,
                });
            }
        },
        updateRestaurantOntopError: function (val, oldVal) {
            if (val && val !== oldVal) {
                this.$notification.error({
                    message: 'CẬP NHẬT ON TOP THẤT BẠI',
                    description: this.updateRestaurantOntopMsg,
                });
            }
        },
        locationList: function (val) {
            this.location.cities = val;
        },
        reAggregateReviewSuccess: function (val, oldVal) {
            if (val && val !== oldVal) {
                this.$notification.success({
                    message: 'TỔNG HỢP LẠI REVIEW THÀNH CÔNG',
                    description: '',
                });
            }
        },
        reAggregateReviewError: function (val, oldVal) {
            if (val && val !== oldVal) {
                this.$notification.error({
                    message: 'TỔNG HỢP LẠI REVIEW THẤT BẠI',
                    description: this.reAggregateReviewMsg,
                });
            }
        },
    },
});
</script>
<style lang="less">
.restaurant-img-wrapper {
    width: 200px;
    height: 200px;
    position: relative;

    .main-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .frame-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        position: absolute;
        left: 0;
        top: 0;
    }
}
</style>
