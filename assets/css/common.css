.ant-breadcrumb {
    border-radius: 12px;
    padding: 10px;
    height: 40px;
    background: #fff;
}

th.ant-descriptions-item-label {
    font-weight: 700;
}

th.ant-descriptions-item-label {
    font-weight: 700;
}

.ant-card {
    border-radius: 12px;
}
.content {
    border-radius: 12px;
}
.content-fluid {
    border-radius: 12px;
    padding: 0px;
    margin-left: 24px;
    margin-right: 24px;
}
.filter-content {
    border-radius: 12px;
}
.w-full {
    width: 100%;
}
.w-320 {
    width: 320px;
}
.flex {
    display: flex;
}

.vill-card {
    margin-bottom: 30px !important;
    width: 100% !important;
}
.primary {
    color: #ffa500;
}

.contradictory {
    color: #005aff;
}

.success {
    color: #28a745;
}
.danger {
    color: #dc3545;
}

.medium {
    font-weight: 600;
}

.strong {
    font-weight: 900;
}

.right {
    order: 2;
}

.left {
    order: 1;
}

@keyframes blink {
    0%,
    100% {
        opacity: 1; /* Fully visible */
    }
    50% {
        opacity: 0; /* Fully transparent */
    }
}

.blinking-element {
    animation: blink 1s 1; /* 1s duration, single iteration */
}

body {
    font-family: sans-serif;
}

.uppercase {
    text-transform: uppercase;
}

.lowercase {
    text-transform: lowercase;
}

.capitalize {
    text-transform: capitalize;
}

.text-red {
    color: red;
}

.text-green {
    color: green;
}

.text-blue {
    color: blue;
}

.text-yellow {
    color: yellow;
}

.text-orange {
    color: orange;
}

.text-black {
    color: black;
}

.text-white {
    color: white;
}

.text-gray {
    color: gray;
}

.text-light-gray {
    color: lightgray;
}

.text-center {
    text-align: center;
}