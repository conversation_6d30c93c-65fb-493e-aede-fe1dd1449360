{
  "editor.minimap.enabled": true,
  "editor.minimap.maxColumn": 70,
  "editor.minimap.scale": 1,
  "editor.minimap.side": "right",
  "editor.minimap.size": "fit",
  "files.exclude": {
    "**/.git": true,
    "**/.svn": true,
    "**/.hg": true,
    "**/CVS": true,
    "**/.DS_Store": true,
    "**/Thumbs.db": true,
    "**/.classpath": false,
    "**/.project": false,
    "**/.settings": false,
    "**/.factorypath": false,
    "**/node_modules": false
  },
  "cSpell.words": [
    "aaaaaaaaacccddeeeeeeegghiiiiilmnnnnooooooprrsssssttuuuuuuuuuwxyyzzz",
    "axios",
    "blowstack",
    "CCCD",
    "chartjs",
    "ckeditor",
    "cmnd",
    "deeplink",
    "Edittor",
    "exceljs",
    "fontkit",
    "geolocator",
    "heremap",
    "Khóa",
    "MANSORY",
    "mapbox",
    "MOMO",
    "Moto",
    "newsfeed",
    "NEWSFEEDS",
    "noti",
    "nuxt",
    "nuxtjs",
    "officedocument",
    "Onepay",
    "openxmlformats",
    "popconfirm",
    "qrcode",
    "QRPAY",
    "spreadsheetml",
    "TNCN",
    "tomtom",
    "vietmap",
    "Vill",
    "VILLBIKE",
    "VILLCAR",
    "VILLEXPRESS",
    "VILLFOOD",
    "VILLMOTO",
    "vuedraggable",
    "vuex",
    "VUNGTAU",
    "ZALO"
  ],
  "vue3snippets.enable-compile-vue-file-on-did-save-code": true,
}