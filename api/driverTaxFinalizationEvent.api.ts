import { DRIVER_TAX_EVENT } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchList = (id: number, params: object) => {
    return httpRequestAuth().post(DRIVER_TAX_EVENT.GET_LIST, {
        tax_finalization_id: id,
        ...params,
    });
};

export const republish = (id: number) => {
    return httpRequestAuth().post(DRIVER_TAX_EVENT.REPUBLISH.replace(':id', id.toString()));
}