import { httpRequestAuth } from "./httpClient";
import { bannerGroups } from "~/constants/apiPaths";

export const fetchList = () => {
  return httpRequestAuth().get(bannerGroups.FETCH_LIST);
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    bannerGroups.FETCH_DETAILS.replace(":id", id.toString())
  );
};

export const update = (id: number, params: object) => {
  return httpRequestAuth().put(
    bannerGroups.UPDATE.replace(":id", id.toString()),
    params
  );
};

export const create = (params: object) => {
  return httpRequestAuth().post(bannerGroups.CREATE, params);
};

export const remove = (id: number) => {
  return httpRequestAuth().delete(
    bannerGroups.DELETE.replace(":id", id.toString())
  );
};

export const fetchBannerList = (id: number) => {
  return httpRequestAuth().get(
    bannerGroups.FETCH_BANNER_LIST.replace(":id", id.toString())
  );
};

export const removeBanner = (id: number) => {
  return httpRequestAuth().delete(
    bannerGroups.REMOVE_BANNER.replace(":id", id.toString())
  );
};

export const createBanner = (params: Object) => {
  return httpRequestAuth().post(bannerGroups.CREATE_BANNER, params);
};

export const updateBanner = (id: number, params: Object) => {
  return httpRequestAuth().put(
    bannerGroups.UPDATE_BANNER.replace(":id", id.toString()),
    params
  );
};

export const fetchBannerDetails = (id: number) => {
  return httpRequestAuth().get(
    bannerGroups.FETCH_BANNER_DETAILS.replace(":id", id.toString())
  );
};

// export const getListPositions = () => {
//     return httpRequestAuth().get(adBanners.LIST_POSITIONS)
// }

// export const getListTypes = () => {
//     return httpRequestAuth().get(adBanners.LIST_TYPES)
// }

export const fetchAdsList = (params: object) => {
  return httpRequestAuth().get(bannerGroups.FETCH_ADS_LIST, { params });
};
