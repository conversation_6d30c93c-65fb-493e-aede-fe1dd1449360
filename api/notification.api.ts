import { httpRequestAuth } from "./httpClient";
import { notifications } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(notifications.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(notifications.FETCH_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(notifications.UPDATE.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(notifications.CREATE, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(notifications.DELETE.replace(':id', id.toString()))
}

export const fetchTopics = () => {
    return httpRequestAuth().get(notifications.GET_NOTIFICATION_TOPICS)
}
