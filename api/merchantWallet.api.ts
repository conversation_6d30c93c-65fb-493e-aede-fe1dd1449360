import { httpRequestAuth } from './httpClient';
import { merchantWallet } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(merchantWallet.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(merchantWallet.FETCH_DETAILS.replace(':id', id.toString()));
};

export const create = (params: object) => {
    return httpRequestAuth().post(merchantWallet.CREATE, params);
};

export const fetchProviders = () => {
    return httpRequestAuth().get(merchantWallet.FETCH_PROVIDERS);
};

export const updateProvider = (id: number, params: object) => {
    return httpRequestAuth().put(merchantWallet.UPDATE_PROVIDER.replace(':id', id.toString()), params);
}