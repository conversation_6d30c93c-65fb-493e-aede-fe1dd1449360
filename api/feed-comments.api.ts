import { httpRequestAuth } from './httpClient';
import { FEED_API_ENDPOINTS, feeds } from '~/constants/apiPaths';

export const fetchList = (params: Record<string, unknown>) => {
    return httpRequestAuth().get(FEED_API_ENDPOINTS.FETCH_LIST, { params });
};

export const fetchReplyList = (params: Record<string, unknown>) => {
    return httpRequestAuth().get(FEED_API_ENDPOINTS.FETCH_REPLY_LIST, { params });
};

export const fetchFullDetail = (params: Record<string, unknown>) => {
    return httpRequestAuth().get(FEED_API_ENDPOINTS.FETCH_FULL_DETAIL, { params });
};

export const fetchPostDetail = (postId: string) => {
    return httpRequestAuth().get(FEED_API_ENDPOINTS.FETCH_POST_DETAIL.replace(':postId', postId));
};

export const deleteComment = (commentId: string) => {
    console.log('commentId', commentId);
    return httpRequestAuth().delete(FEED_API_ENDPOINTS.DELETE_COMMENT.replace(':commentId', commentId));
};

export const fetchCommentReportList = (params: Record<string, unknown>) => {
    return httpRequestAuth().get(feeds.GET_COMMENT_REPORT_LIST, { params });
};

export const updateCommentReport = (params: Record<string, unknown>) => {
    return httpRequestAuth().put(feeds.UPDATE_COMMENT_REPORT, params);
};