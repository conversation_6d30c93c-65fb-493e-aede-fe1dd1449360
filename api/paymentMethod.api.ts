import { httpRequestAuth } from "./httpClient";
import { paymentMethods } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(paymentMethods.FETCH_LIST, {params})
}

export const fetchAll = () => {
    return httpRequestAuth().get(paymentMethods.FETCH_ALL)
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(paymentMethods.FETCH_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(paymentMethods.UPDATE.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(paymentMethods.CREATE, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(paymentMethods.DELETE.replace(':id', id.toString()))
}