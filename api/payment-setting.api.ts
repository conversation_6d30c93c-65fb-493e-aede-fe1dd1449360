import { EPaymentSettingKey } from '~/constants/setting';
import { httpRequestAuth } from './httpClient';
import { PAYMENT_SETTING } from '~/constants/apiPaths';

export const fetchVillFoodSettingFees = (provinceId: number) => {
    return httpRequestAuth().get(PAYMENT_SETTING.FETCH_VILL_PAYMENT_SETTING_FEES, {
        params: { key: EPaymentSettingKey.VILL_FOOD_SETTING_FEES_V2, subProvinceId: provinceId },
    });
};

export const fetchVillBikeSettingFees = (provinceId: number) => {
    return httpRequestAuth().get(PAYMENT_SETTING.FETCH_VILL_PAYMENT_SETTING_FEES, {
        params: { key: EPaymentSettingKey.VILL_BIKE_SETTING_FEES_V2, subProvinceId: provinceId },
    });
};

export const fetchVillExpressSettingFees = (provinceId: number) => {
    return httpRequestAuth().get(PAYMENT_SETTING.FETCH_VILL_PAYMENT_SETTING_FEES, {
        params: { key: EPaymentSettingKey.VILL_EXPRESS_SETTING_FEES_V2, subProvinceId: provinceId },
    });
};

export const updateVillFoodSettingFees = (data: any, provinceId: number) => {
    return httpRequestAuth().patch(PAYMENT_SETTING.UPDATE_VILL_PAYMENT_SETTING_FEES, {
        key: EPaymentSettingKey.VILL_FOOD_SETTING_FEES_V2,
        data,
        subProvinceId: provinceId,
    });
};

export const updateVillBikeSettingFees = (data: any, provinceId: number) => {
    return httpRequestAuth().patch(PAYMENT_SETTING.UPDATE_VILL_PAYMENT_SETTING_FEES, {
        key: EPaymentSettingKey.VILL_BIKE_SETTING_FEES_V2,
        data,
        subProvinceId: provinceId,
    });
};

export const updateVillExpressSettingFees = (data: any, provinceId: number) => {
    return httpRequestAuth().patch(PAYMENT_SETTING.UPDATE_VILL_PAYMENT_SETTING_FEES, {
        key: EPaymentSettingKey.VILL_EXPRESS_SETTING_FEES_V2,
        data,
        subProvinceId: provinceId,
    });
};
