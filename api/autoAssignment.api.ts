import { httpRequestAuth } from "./httpClient";
import { autoAssign } from "~/constants/apiPaths";
import {getAppConfigs} from '../configs';

export const getQueueStatus = () => {
    return httpRequestAuth({
        DOMAIN: getAppConfigs().AUTO_ORDER_ASSIGNED_DOMAIN
    }).get(autoAssign.QUEUE_STATUS)
}

export const startQueue = () => {
    return httpRequestAuth({
        DOMAIN: getAppConfigs().AUTO_ORDER_ASSIGNED_DOMAIN
    }).post(autoAssign.START_QUEUE)
}

export const stopQueue = () => {
    return httpRequestAuth({
        DOMAIN: getAppConfigs().AUTO_ORDER_ASSIGNED_DOMAIN
    }).post(autoAssign.STOP_QUEUE)
}