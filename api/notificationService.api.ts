import { notificationService } from "~/constants/apiPaths";
import { httpRequestAuth } from "./httpClient";

export const fetchList = (params: object) => {
  return httpRequestAuth().get(notificationService.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    notificationService.FETCH_DETAILS.replace(":id", id.toString())
  );
};

export const update = (id: number, params: object) => {
  return httpRequestAuth().put(
    notificationService.UPDATE.replace(":id", id.toString()),
    params
  );
};

export const create = (params: object) => {
  return httpRequestAuth().post(notificationService.CREATE, params);
};
