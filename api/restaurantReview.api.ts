import { httpRequestAuth } from "./httpClient";
import { restaurantReviews } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
  return httpRequestAuth().get(restaurantReviews.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    restaurantReviews.FETCH_DETAILS.replace(":id", id.toString())
  );
};

export const remove = (id: number) => {
  return httpRequestAuth().delete(
    restaurantReviews.DELETE.replace(":id", id.toString())
  );
};

export const updateReviewIsDisplayed = (id: number, params: object) => {
  return httpRequestAuth().put(
    restaurantReviews.UPDATE_IS_DISPLAYED.replace(":id", id.toString()),
    params
  );
};

export const updateReplyIsDisplayed = (id: number, params: object) => {
  return httpRequestAuth().put(
    restaurantReviews.UPDATE_REPLY_IS_DISPLAYED.replace(":id", id.toString()),
    params
  );
};
