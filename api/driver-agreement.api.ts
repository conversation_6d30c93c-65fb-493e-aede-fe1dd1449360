import { httpRequestAuth } from './httpClient';
import { DRIVER_AGREEMENT } from '~/constants/apiPaths';

export const fetchList = (driverId: string) => {
  return httpRequestAuth().get(DRIVER_AGREEMENT.FETCH_LIST.replace(':driverId', driverId));
};

export const create = (data: Object) => {
  return httpRequestAuth().post(DRIVER_AGREEMENT.CREATE, data);
};

export const update = (id: number) => {
  return httpRequestAuth().patch(DRIVER_AGREEMENT.UPDATE.replace(':id', id.toString()));
};

export const show = (id: number) => {
  return httpRequestAuth().patch(DRIVER_AGREEMENT.SHOW.replace(':id', id.toString()));
};

export const presignedUrl = (id: number) => {
  return httpRequestAuth().patch(DRIVER_AGREEMENT.UPDATE.replace(':id', id.toString()));
};

export const generate = (data: Object) => {
  return httpRequestAuth().post(DRIVER_AGREEMENT.GENERATE, data);
};
