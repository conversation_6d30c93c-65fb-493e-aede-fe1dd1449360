import { httpRequestAuth } from "./httpClient";
import { popups } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(popups.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(popups.FETCH_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(popups.UPDATE.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(popups.CREATE, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(popups.DELETE.replace(':id', id.toString()))
}