import { httpRequestAuth } from "./httpClient";
import { uploadFiles } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(uploadFiles.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(uploadFiles.FETCH_DETAILS.replace(':id', id.toString()))
}

export const create = (params: object) => {
    return httpRequestAuth().post(uploadFiles.CREATE, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(uploadFiles.DELETE.replace(':id', id.toString()))
}

export const getPresignedUrl = (params: Record<string, string>) => {
    return httpRequestAuth().post(uploadFiles.GET_PRESIGNED_URL, params)
}

