import { httpRequestAuth } from './httpClient';
import { restaurantOrderFee } from '~/constants/apiPaths';

export const fetchList = (id: number) => {
    return httpRequestAuth().get(restaurantOrderFee.FETCH_LIST.replace(':restaurantId', id.toString()));
};

export const update = (id: number, restaurantId: number, params: object) => {
    return httpRequestAuth().put(restaurantOrderFee.UPDATE.replace(':id', id.toString()).replace(":restaurantId", restaurantId.toString()), params);
};

export const create = (params: object) => {
    return httpRequestAuth().post(restaurantOrderFee.CREATE, params);
};

export const deleteById = (id: number) => {
    return httpRequestAuth().delete(restaurantOrderFee.DELETE.replace(':id', id.toString()));
}
