import { httpRequestAuth } from "./httpClient";
import { faceRecognitionEnrollmentType } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
  return httpRequestAuth().get(faceRecognitionEnrollmentType.FETCH_LIST, {
    params,
  });
};

export const fetchDetail = (id: number) => {
  return httpRequestAuth().get(
    faceRecognitionEnrollmentType.FETCH_DETAIL.replace(":id", id.toString())
  );
};

export const remove = (id: number) => {
  return httpRequestAuth().delete(
    faceRecognitionEnrollmentType.DELETE.replace(":id", id.toString())
  );
};

export const update = (id: number, params: object) => {
  return httpRequestAuth().put(
    faceRecognitionEnrollmentType.UPDATE.replace(":id", id.toString()),
    params
  );
};

export const create = (params: object) => {
  return httpRequestAuth().post(faceRecognitionEnrollmentType.CREATE, params);
};
