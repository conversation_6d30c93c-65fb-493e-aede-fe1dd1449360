import { httpRequestAuth } from "./httpClient";
import { loyalty } from "~/constants/apiPaths";

export const fetchProgramList = () => {
  return httpRequestAuth().get(loyalty.FETCH_PROGRAM_LIST);
};
export const fetchProgramDetails = (id: number) => {
  return httpRequestAuth().get(
    loyalty.FETCH_PROGRAM_DETAILS.replace(":id", id.toString())
  );
};
export const createProgram = (params: object) => {
  return httpRequestAuth().post(loyalty.CREATE_PROGRAM, params);
};
export const updateProgram = (id: number, params: object) => {
  return httpRequestAuth().put(
    loyalty.UPDATE_PROGRAM.replace(":id", id.toString()),
    params
  );
};
export const deleteProgram = (id: number) => {
  return httpRequestAuth().delete(
    loyalty.DELETE_PROGRAM.replace(":id", id.toString())
  );
};

export const fetchAccountList = (params: object) => {
  return httpRequestAuth().get(loyalty.FETCH_ACCOUNT_LIST, { params });
};

export const fetchAccountTypes = () => {
  return httpRequestAuth().get(loyalty.FETCH_ACCOUNT_TYPES);
};

export const updateAccountType = (id: number, params: object) => {
  return httpRequestAuth().put(loyalty.UPDATE_ACCOUNT_TYPE.replace(":id", id.toString()), params);
};

export const fetchPromotionRewardTransactionsList = (params: object) => {
  return httpRequestAuth().get(
    loyalty.FETCH_PROMOTION_REWARD_TRANSACTION_LIST,
    { params }
  );
};
export const fetchPromotionRewardTransactionsDetails = (id: number) => {
  return httpRequestAuth().get(
    loyalty.FETCH_PROMOTION_REWARD_TRANSACTION_DETAILS.replace(
      ":id",
      id.toString()
    )
  );
};
export const fetchReferralEventHookLogsList = (params: object) => {
  return httpRequestAuth().get(loyalty.FETCH_REFERRAL_EVENT_HOOK_LOG_LIST, {
    params
  });
};
export const fetchReferralEventHookLogsDetails = (id: number) => {
  return httpRequestAuth().get(
    loyalty.FETCH_REFERRAL_EVENT_HOOK_LOG_DETAILS.replace(":id", id.toString())
  );
};
export const fetchRollCallAccountLogsList = (params: object) => {
  return httpRequestAuth().get(loyalty.FETCH_ROLLCALL_ACCOUNT_LOG_LIST, {
    params
  });
};
export const fetchRollCallAccountLogsDetails = (id: number) => {
  return httpRequestAuth().get(
    loyalty.FETCH_REFERRAL_EVENT_HOOK_LOG_DETAILS.replace(":id", id.toString())
  );
};
export const fetchTransactionsList = (params: object) => {
  return httpRequestAuth().get(loyalty.FETCH_TRANSACTION_LIST, { params });
};
export const fetchTransactionsDetails = (id: number) => {
  return httpRequestAuth().get(
    loyalty.FETCH_TRANSACTION_DETAILS.replace(":id", id.toString())
  );
};

export const fetchPromotionRewardList = (params: object) => {
  return httpRequestAuth().get(loyalty.FETCH_PROMOTION_REWARD_LIST, { params });
};
export const fetchPromotionRewardDetails = (id: number) => {
  return httpRequestAuth().get(
    loyalty.FETCH_PROMOTION_REWARD_DETAILS.replace(":id", id.toString())
  );
};
export const createPromotionReward = (params: object) => {
  return httpRequestAuth().post(loyalty.CREATE_PROMOTION_REWARD, params);
};
export const updatePromotionReward = (id: number, params: object) => {
  return httpRequestAuth().put(
    loyalty.UPDATE_PROMOTION_REWARD.replace(":id", id.toString()),
    params
  );
};
export const deletePromotionReward = (id: number) => {
  return httpRequestAuth().delete(
    loyalty.DELETE_PROMOTION_REWARD.replace(":id", id.toString())
  );
};

export const fetchEventHookList = (params: object) => {
  return httpRequestAuth().get(loyalty.FETCH_EVENT_HOOK_LIST, { params });
};
export const fetchEventHookDetails = (id: number) => {
  return httpRequestAuth().get(
    loyalty.FETCH_EVENT_HOOK_DETAILS.replace(":id", id.toString())
  );
};
export const createEventHook = (params: object) => {
  return httpRequestAuth().post(loyalty.CREATE_EVENT_HOOK, params);
};
export const updateEventHook = (id: number, params: object) => {
  return httpRequestAuth().put(
    loyalty.UPDATE_EVENT_HOOK.replace(":id", id.toString()),
    params
  );
};
export const deleteEventHook = (id: number) => {
  return httpRequestAuth().delete(
    loyalty.DELETE_EVENT_HOOK.replace(":id", id.toString())
  );
};
export const fetchMissionList = (params: object) => {
  return httpRequestAuth().get(loyalty.FETCH_MISSION_LIST, { params });
};
export const fetchMissionDetails = (id: number) => {
  return httpRequestAuth().get(
    loyalty.FETCH_MISSION_DETAILS.replace(":id", id.toString())
  );
};
export const createMission = (params: object) => {
  return httpRequestAuth().post(loyalty.CREATE_MISSION, params);
};
export const updateMission = (id: number, params: object) => {
  return httpRequestAuth().put(
    loyalty.UPDATE_MISSION.replace(":id", id.toString()),
    params
  );
};
export const fetchMissionTypeList = () => {
  return httpRequestAuth().get(loyalty.FETCH_MISSION_TYPE_LIST);
};
export const fetchMissionMember = (params: object) => {
  return httpRequestAuth().get(loyalty.FETCH_MISSION_MEMBER_LIST, { params });
};
