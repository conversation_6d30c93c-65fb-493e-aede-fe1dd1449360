import { httpRequestAuth } from "./httpClient";
import { promotionMarket } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
  return httpRequestAuth().get(promotionMarket.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    promotionMarket.FETCH_DETAILS.replace(":id", id.toString())
  );
};

export const update = (id: number, params: object) => {
  return httpRequestAuth().put(
    promotionMarket.UPDATE.replace(":id", id.toString()),
    params
  );
};

export const create = (params: object) => {
  return httpRequestAuth().post(promotionMarket.CREATE, params);
};

export const remove = (id: number) => {
  return httpRequestAuth().delete(
    promotionMarket.DELETE.replace(":id", id.toString())
  );
};

export const fetchPromoMarketRestaurantsList = (id: number, params: object) => {
  return httpRequestAuth().get(
    promotionMarket.FETCH_RESTAURANT_LIST.replace(
      ":promoMarketId",
      id.toString()
    ),
    { params }
  );
};

export const removeRestaurantFromPromoMarket = (
  promoMarketId: number,
  restaurantId: number
) => {
  return httpRequestAuth().delete(
    promotionMarket.REMOVE_RESTAURANT.replace(
      ":promoMarketId",
      promoMarketId.toString()
    ).replace(":restaurantId", restaurantId.toString())
  );
};
