import { httpRequest, httpRequestAuth } from './httpClient';
import { orders } from '~/constants/apiPaths';

export const fetchOrderList = (params: object) => {
    return httpRequestAuth().get(orders.FETCH_ORDER_LIST, {
        params: params,
    });
};

export const fetchLast3MonthsOrderList = (params: object) => {
    return httpRequestAuth().get(orders.FETCH_LAST_3_MONTHS_ORDER_LIST, {
        params: params,
    });
};

export const fetchOrderDetail = (id: number) => {
    return httpRequestAuth().get(orders.FETCH_ORDER_DETAIL + id);
};

export const updateOrder = (id: number, params: any) => {
    return httpRequestAuth().put(orders.UPDATE_ORDER + id, params);
};

export const deleteOrder = (id: number) => {
    return httpRequestAuth().delete(orders.DELETE_ORDER + id);
};

export const fetchAllOrderStatuses = () => {
    return httpRequestAuth().get(orders.FETCH_ORDER_STATUSES);
};

export const fetchOrderStatusDetail = (id: number) => {
    return httpRequestAuth().get(orders.FETCH_ORDER_STATUS_DETAIL + id);
};

export const updateOrderStatus = ({ id, status }: any) => {
    return httpRequestAuth().put(orders.FETCH_ORDER_STATUS_DETAIL + id, {
        status,
    });
};

export const assignShipper = ({ orderId, shipperId }: any) => {
    return httpRequestAuth().put(orders.ASSIGN_SHIPPER.replace(':id', orderId), {
        shipperId,
    });
};

export const fetchOrderHistoryList = (params: object) => {
    return httpRequestAuth().get(orders.FETCH_ORDER_HISTORY_LIST, {
        params: params,
    });
};

export const fetchOrderHistoryDetails = (id: number) => {
    return httpRequestAuth().get(orders.FETCH_ORDER_HISTORY_DETAILS.replace(':id', id.toString()));
};

export const fetchOrderDriverExpense = (id: number) => {
    return httpRequestAuth().get(orders.FETCH_ORDER_DRIVER_EXPENSE.replace(':id', id.toString()));
};
