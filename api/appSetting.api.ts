import { httpRequestAuth } from './httpClient';
import { appSettings } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(appSettings.FETCH_LIST, { params });
};

export const fetchDetailsByKey = (key: string) => {
    return httpRequestAuth().get(appSettings.FETCH_BY_KEY.replace(':key', key));
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(appSettings.FETCH_DETAILS.replace(':id', id.toString()));
};

export const update = (params: object) => {
    return httpRequestAuth().put(appSettings.UPDATE, params);
};

export const updateSupportContact = (params: object) => {
    return httpRequestAuth().put(appSettings.UPDATE_SUPPORT_CONTACT, params);
};

export const create = (params: object) => {
    return httpRequestAuth().post(appSettings.CREATE, params);
};

export const remove = (id: number) => {
    return httpRequestAuth().delete(appSettings.DELETE.replace(':id', id.toString()));
};

export const resetCache = () => {
    return httpRequestAuth().delete(appSettings.RESET_CACHE);
};

export const getSubSettings = (subProvinceId: number) => {
    return httpRequestAuth().get(appSettings.FETCH_SUB_SETTINGS, {
        params: { subProvinceId },
    });
};

export const updateSubSettings = (body: Record<string, any>) => {
    return httpRequestAuth().put(appSettings.UPDATE_SUB_SETTINGS_BY_KEY, body);
};

export const addCollectionBroadcastNoti = (body: Record<string, any>) => {
    return httpRequestAuth().put(appSettings.ADD_COLLECTION_BROADCAST_NOTI, body);
};

export const getRestaurantTradeDiscount = () => {
    return httpRequestAuth().get(appSettings.GET_RESTAURANT_TRADE_DISCOUNT);
};