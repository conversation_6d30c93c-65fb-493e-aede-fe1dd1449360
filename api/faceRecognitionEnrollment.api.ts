import { httpRequestAuth } from "./httpClient";
import { faceRecognitionEnrollment } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
  return httpRequestAuth().get(faceRecognitionEnrollment.FETCH_LIST, {
    params,
  });
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    faceRecognitionEnrollment.FETCH_DETAILS.replace(":id", id.toString())
  );
};

export const update = (id: number, params: object) => {
  console.log(id);
  return httpRequestAuth().put(
    faceRecognitionEnrollment.UPDATE.replace(":id", id.toString()),
    params
  );
};
