import { httpRequestAuth } from './httpClient';
import { EMPLOYEE_ROLES } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(EMPLOYEE_ROLES.FETCH_ROLE_LIST, { params });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(EMPLOYEE_ROLES.FETCH_ROLE_DETAILS.replace(':id', id.toString()));
};

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(EMPLOYEE_ROLES.UPDATE_ROLE.replace(':id', id.toString()), params);
};

export const create = (params: object) => {
    return httpRequestAuth().post(EMPLOYEE_ROLES.CREATE_ROLE, params);
};

export const deleteRole = (id: number) => {
    return httpRequestAuth().delete(EMPLOYEE_ROLES.DELETE_ROLE.replace(':id', id.toString()));
};
