import axios from 'axios';
import { getAppConfigs } from '../configs';
import { getUserTokenStorage, getProvinceIdStorage, getUserRefreshToken, setUserTokenStorage, setUserRefreshToken } from '../utils/common';
import { renewToken } from './user.api';
import { HTTP_UNAUTHORIZE_CODES, LOCAL_STORAGE_REFRESH_TOKEN, LOCAL_STORAGE_USER_TOKEN } from '~/constants/constants';
import localStorageHandler from '~/utils/localStorageHandler';

interface HttpRequestConfigInterface {
    DOMAIN?: string;
    headers?: Object;
}

const REQUEST_TIMEOUT = 120000;

const requestHeaders = () => ({
    Authorization: 'Bearer ' + getUserTokenStorage() || '',
    'X-Province': getProvinceIdStorage(),
});

export const httpRequest = (configs?: HttpRequestConfigInterface) => {
    const { APP_DOMAIN } = getAppConfigs();

    const baseURL = (configs && configs.DOMAIN) || APP_DOMAIN;
    const customHeaders = (configs && configs.headers) || {};
    return axios.create({
        baseURL: baseURL,
        timeout: REQUEST_TIMEOUT,
        headers: {
            'X-Province': getProvinceIdStorage(),
            ...customHeaders,
        },
    });
};

export const httpRequestAuth = (configs?: HttpRequestConfigInterface) => {
    const { APP_DOMAIN } = getAppConfigs();
    const baseURL = (configs && configs.DOMAIN) || APP_DOMAIN;
    const customHeaders = (configs && configs.headers) || {};
    const instance = axios.create({
        baseURL: baseURL,
        timeout: REQUEST_TIMEOUT,
        headers: {
            ...requestHeaders(),
            ...customHeaders,
        },
    });
    instance.interceptors.response.use(
        function (response) {
            return response;
        },
        async function (error) {
            if (error.response && HTTP_UNAUTHORIZE_CODES.includes(error.response.status)) {
                const refreshToken = getUserRefreshToken();
                if (refreshToken) {
                    try {
                        const tokens = (await renewToken({ refreshToken })).data.data;
                        const newToken = tokens.token;
                        const newRefreshToken = tokens.refreshToken;
                        setUserTokenStorage(newToken);
                        setUserRefreshToken(newRefreshToken);
                        const { config } = error;
                        const { headers } = config;
                        headers.Authorization = 'Bearer ' + newToken;
                        const response = await httpRequestAuth({}).request(config);
                        console.log('response', response.status);
                        if (HTTP_UNAUTHORIZE_CODES.includes(response.status)) {
                            handleUserRefreshTokenExpired();
                        } else {
                            return response;
                        }
                    } catch (err: any) {
                        if (err.response && HTTP_UNAUTHORIZE_CODES.includes(err.response.status)) {
                            handleUserRefreshTokenExpired();
                            window.location.assign('/login');
                        }
                    }
                } else {
                    handleUserRefreshTokenExpired();
                }
            }
            return Promise.reject(error);
        },
    );
    return instance;
};

function handleUserRefreshTokenExpired() {
    localStorageHandler.removeItem(LOCAL_STORAGE_USER_TOKEN);
    localStorageHandler.removeItem(LOCAL_STORAGE_REFRESH_TOKEN);
    window.location.assign('/login');
}
