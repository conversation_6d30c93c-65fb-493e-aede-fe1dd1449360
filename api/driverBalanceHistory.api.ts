import { httpRequestAuth } from './httpClient';
import { DRIVER_BALANCE_HISTORY } from '~/constants/apiPaths';

export const fetchDriverBalanceHistories = async (params: any) => {
    return await httpRequestAuth().get(DRIVER_BALANCE_HISTORY.GET_DRIVER_BALANCE_HISTORY, { params });
};

export const fetchDriverBalanceHistorySummary = async (params: any) => {
    return await httpRequestAuth().get(DRIVER_BALANCE_HISTORY.GET_SUMMARY, { params });
};
