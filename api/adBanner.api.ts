import { httpRequestAuth } from "./httpClient";
import { adBanners } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(adBanners.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(adBanners.FETCH_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(adBanners.UPDATE.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(adBanners.CREATE, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(adBanners.DELETE.replace(':id', id.toString()))
}

export const getListPositions = () => {
    return httpRequestAuth().get(adBanners.LIST_POSITIONS)
}

export const getListTypes = () => {
    return httpRequestAuth().get(adBanners.LIST_TYPES)
}