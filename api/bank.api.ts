import { httpRequestAuth } from "./httpClient";
import { banks } from "~/constants/apiPaths";

export const fetchList = () => {
    return httpRequestAuth().get(banks.GET_BANKS);
}

export const getBankAccount = (query: Record<string, string>) => {
    return httpRequestAuth().get(banks.GET_BANK_ACCOUNT, {
        params: query
    });
}

export const getEpaySupportBanks = () => {
    return httpRequestAuth().get(banks.GET_EPAY_SUPPORT_BANK);
}

export const get9PaySupportBanks = () => {
    return httpRequestAuth().get(banks.GET_9PAY_SUPPORT_BANK);
}