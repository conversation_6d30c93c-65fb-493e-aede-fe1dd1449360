import { wallets } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchWalletDetails = (id: number) => {
    return httpRequestAuth().get(wallets.FETCH_WALLET_DETAILS.replace(':id', id.toString()));
};

export const createTransaction = (params: object) => {
    return httpRequestAuth().post(wallets.CREATE_TRANSACTION, params);
};

export const fetchTransactions = (params: object) => {
    return httpRequestAuth().get(wallets.FETCH_TRANSACTIONS, { params });
};

export const retryTransaction = (id: number) => {
    return httpRequestAuth().put(wallets.RETRY_TRANSACTION.replace(':id', id.toString()));
};

export const confirmTransaction = (id: number) => {
    return httpRequestAuth().put(wallets.CONFIRM_TRANSACTION.replace(':id', id.toString()));
};

export const rejectTransaction = (id: number) => {
    return httpRequestAuth().put(wallets.REJECT_TRANSACTION.replace(':id', id.toString()));
};

export const removeTransaction = (id: number) => {
    return httpRequestAuth().delete(wallets.REMOVE_TRANSACTION.replace(':id', id.toString()));
};

export const createWallet = (params: object) => {
    return httpRequestAuth().post(wallets.CREATE_WALLET, params);
};

export const walletDashboard = () => {
    return httpRequestAuth().get(wallets.WALLET_DASHBOARD);
};

export const exportTransactions = (params: object) => {
    return httpRequestAuth().get(wallets.EXPORT_TRANSACTIONS, { params });
};

export const getWallets = (params: object) => {
    return httpRequestAuth().get(wallets.GET_WALLETS, { params });
};

export const getWalletsByDriverId = (driverId: number) => {
    return httpRequestAuth().get(wallets.FETCH_WALLET_BY_DRIVER_ID.replace(':id', driverId.toString()));
};

export const getRevenueAndExpenditureByDate = (params: object) => {
    return httpRequestAuth().get(wallets.GET_TOTAL_REVENUE_AND_TOTAL_EXPENDITURE_BY_DATE, { params });
};

export const getPendingTransactions = () => {
    return httpRequestAuth().get(wallets.GET_PENDING_TRANSACTIONS);
};

export const confirmTransferCashWithWithdrawalTransaction = (id: number) => {
    return httpRequestAuth().put(wallets.CASHIER_CONFIRM_WITHDRAWAL_TRANSACTION.replace(':id', id + ''));
};

export const onepayPayoutFetchAndUpdate = (id: number) => {
    return httpRequestAuth().put(wallets.ONEPAY_PAYOUT_FETCH_AND_UPDATE.replace(':id', id + ''));
};

export const getWalletProviders = () => {
    return httpRequestAuth().get(wallets.FETCH_WALLET_PROVIDERS);
};

export const updateWalletProviderActiveStatus = (id: number, active: number) => {
    return httpRequestAuth().put(wallets.UPDATE_WALLET_PROVIDER_ACTIVE_STATUS.replace(':id', id.toString()), {
        active,
    });
};

export const updateWalletProviderServiceActiveStatus = (id: number, active: number) => {
    return httpRequestAuth().put(wallets.UPDATE_WALLET_PROVIDER_SERVICE_ACTIVE_STATUS.replace(':id', id.toString()), {
        active,
    });
};
