import { httpRequestAuth } from './httpClient';
import { DRIVER_AGREEMENT_TYPE } from '~/constants/apiPaths';

export const fetchList = (params: Object) => {
  return httpRequestAuth().get(DRIVER_AGREEMENT_TYPE.FETCH_LIST, { params });
};

export const create = (data: Object) => {
  return httpRequestAuth().post(DRIVER_AGREEMENT_TYPE.CREATE, data);
};

export const remove = (id: number) => {
  return httpRequestAuth().delete(DRIVER_AGREEMENT_TYPE.DELETE.replace(':id', id.toString()));
};

export const getDataKeys = () => {
  return httpRequestAuth().get(DRIVER_AGREEMENT_TYPE.GET_DATA_KEYS);
};

export const getPresignedUrlForUploadTemplate = () => {
  return httpRequestAuth().get(DRIVER_AGREEMENT_TYPE.GET_PRESIGNED_URL_FOR_UPLOAD_TEMPLATE);
};

export const update = (id: number, body: Object) => {
  return httpRequestAuth().patch(DRIVER_AGREEMENT_TYPE.UPDATE_IS_ACTIVE.replace(':id', id.toString()), body);
};

export const getDetails = (id: number) => {
  return httpRequestAuth().get(DRIVER_AGREEMENT_TYPE.GET_DETAILS.replace(':id', id.toString()));
};

export const getPresignedUrlForUpdate = (params: number) => {
  return httpRequestAuth().get(DRIVER_AGREEMENT_TYPE.GET_PRESIGNED_URL_FOR_UPDATE_TEMPLATE.replace(':id', params.toString()));
} 