import { cronjobs } from "~/constants/apiPaths"
import { httpRequestAuth } from "./httpClient"

export const fetchList = () => {
    return httpRequestAuth().get(cronjobs.FETCH_LIST)
}
export const aggregateWeekOrders = () =>{
    return httpRequestAuth().get(cronjobs.AGGREGATE_WEEK_ORDERS)
}
export const aggregateWeekRating = () =>{
    return httpRequestAuth().get(cronjobs.AGGREGATE_WEEK_RATING)
}
export const aggregatePrevWeekRating = () =>{
    return httpRequestAuth().get(cronjobs.AGGREGATE_PREV_WEEK_RATING)
}