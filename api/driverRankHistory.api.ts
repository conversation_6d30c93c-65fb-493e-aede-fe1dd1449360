import { httpRequestAuth } from "./httpClient";
import { drivers } from "~/constants/apiPaths";

export const fetchList = (id: number, params: Object) => {
  return httpRequestAuth().get(
    drivers.FETCH_DRIVER_RANK_HISTORY_LIST.replace(":id", id.toString()),
    { params }
  );
};

export const fetchDetails = (historyId: number) => {
  return httpRequestAuth().get(
    drivers.FETCH_DRIVER_RANK_HISTORY_DETAILS.replace(
      ":historyId",
      historyId.toString()
    )
  );
};
