import { httpRequestAuth } from './httpClient';

export const getContactAddress = (driverId: number) => {
    return httpRequestAuth().get(`api/drivers/${driverId}/contact-address`);
};

export const createContactAddress = (driverId: number, data: object) => {
    return httpRequestAuth().post(`api/drivers/${driverId}/contact-address`, data);
};

export const updateContactAddress = (driverId: number, data: object) => {
    return httpRequestAuth().put(`api/drivers/${driverId}/contact-address`, data);
};
