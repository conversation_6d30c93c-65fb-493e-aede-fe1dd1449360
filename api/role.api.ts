import { httpRequestAuth } from "./httpClient";
import { roles } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
  return httpRequestAuth().get(roles.FETCH_ROLE_LIST, { params });
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    roles.FETCH_ROLE_DETAILS.replace(":id", id.toString())
  );
};

export const update = (id: number, params: object) => {
  console.log("hreeg", id, params);

  return httpRequestAuth().put(
    roles.UPDATE_ROLE.replace(":id", id.toString()),
    params
  );
};

export const create = (params: object) => {
  return httpRequestAuth().post(roles.CREATE_ROLE, params);
};

export const deleteRole = (id: number) => {
  return httpRequestAuth().delete(
    roles.DELETE_ROLE.replace(":id", id.toString())
  );
};
