import { driverReport } from "./../constants/apiPaths";
import { httpRequestAuth } from "./httpClient";

export const fetchList = (params: Object): any => {
  return httpRequestAuth().get(driverReport.FETCH_LIST, { params });
};

export const deleteReport = (id: number): any => {
  return httpRequestAuth().delete(
    driverReport.DELETE.replace(":id", id.toString())
  );
};

export const fetchDetails = (id: number): any => {
  return httpRequestAuth().get(
    driverReport.FETCH_DETAILS.replace(":id", id.toString())
  );
};
