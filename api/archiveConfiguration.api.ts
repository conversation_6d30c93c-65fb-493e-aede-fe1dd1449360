import { httpRequestAuth } from "./httpClient";
import { archiveConfiguration } from "~/constants/apiPaths";

export const fetchAll = () => {
    return httpRequestAuth({
        // DOMAIN: 'http://localhost:3000',
    }).get(archiveConfiguration.FETCH_ALL)
}

export const update = (key: string, body: object) => {
    return httpRequestAuth({
        // DOMAIN: 'http://localhost:3000',
    }).put(archiveConfiguration.UPDATE.replace(':key', key), body)
}
