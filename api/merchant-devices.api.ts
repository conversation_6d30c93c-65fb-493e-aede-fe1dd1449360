import { httpRequestAuth } from './httpClient';
import { MERCHANT_DEVICES } from '~/constants/apiPaths';

export const fetchDevices = (merchant_id: number) => {
    return httpRequestAuth().get(MERCHANT_DEVICES.FETCH_LIST, {
        params: {
            merchant_id,
        },
    });
};

export const updateDevice = (id: number, params: Record<string, any>) => {
    return httpRequestAuth().post(MERCHANT_DEVICES.UPDATE.replace(':id', id.toString()), params);
};

export const deleteDevice = (id: number) => {
    return httpRequestAuth().delete(MERCHANT_DEVICES.DELETE.replace(':id', id.toString()));
};

export const removeAllDevices = (merchant_id: number) => {
    return httpRequestAuth().delete(MERCHANT_DEVICES.REMOVE_ALL.replace(':merchant_id', merchant_id.toString()));
};
