import { httpRequestAuth } from "./httpClient";
import { archiveExportFile } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth({
        // DOMAIN: 'http://localhost:3000'
    }).get(archiveExportFile.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth({
        // DOMAIN: 'http://localhost:3000'
    }).get(archiveExportFile.FETCH_DETAILS.replace(':id', id.toString()))
}

export const downloadFile = (id: number, params: object) => {
    return httpRequestAuth({
        // DOMAIN: 'http://localhost:3000'
    }).get(archiveExportFile.DOWNLOAD_FILE.replace(':id', id.toString()), {params})
}
