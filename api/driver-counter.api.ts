import { DRIVER_COUNTER } from './../constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(DRIVER_COUNTER.FETCH_DRIVER_COUNTER_LIST, { params });
};

export const fetchItem = (id: string) => {
    return httpRequestAuth().get(DRIVER_COUNTER.FETCH_DRIVER_COUNTER.replace(':id', id));
};

export const create = (data: object) => {
    return httpRequestAuth().post(DRIVER_COUNTER.CREATE_DRIVER_COUNTER, data);
};

export const update = (data: any) => {
    return httpRequestAuth().put(DRIVER_COUNTER.UPDATE_DRIVER_COUNTER.replace(':id', data.id), data);
};

export const remove = (id: string) => {
    return httpRequestAuth().delete(DRIVER_COUNTER.DELETE_DRIVER_COUNTER.replace(':id', id));
};

export const generateCode = (id: string) => {
    return httpRequestAuth().patch(DRIVER_COUNTER.REGENERATE_DRIVER_COUNTER.replace(':id', id));
};
