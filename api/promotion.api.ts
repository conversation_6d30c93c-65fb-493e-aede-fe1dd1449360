import { httpRequestAuth } from './httpClient';
import { promotions } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(promotions.FETCH_LIST, { params });
};

export const fetchUsers = (id: number, params: object) => {
    return httpRequestAuth().get(promotions.FETCH_USERS.replace(':id', id.toString()), { params });
};

export const fetchAllUser = (params: object) => {
    return httpRequestAuth().get(promotions.FETCH_ALL_USER, { params });
};

export const fetchAllRestaurant = (params: object) => {
    return httpRequestAuth().get(promotions.FETCH_ALL_RESTAURANT, { params });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(promotions.FETCH_DETAILS.replace(':id', id.toString()));
};

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(promotions.UPDATE.replace(':id', id.toString()), params);
};

export const create = (params: object) => {
    return httpRequestAuth().post(promotions.CREATE, params);
};

export const remove = (id: number) => {
    return httpRequestAuth().delete(promotions.DELETE.replace(':id', id.toString()));
};

export const addUserToPromotion = ({ promoId, userId }: any) => {
    return httpRequestAuth().put(
        promotions.ADD_USER_TO_PROMOTION.replace(':promoId', promoId.toString()).replace(':userId', '' + userId),
    );
};

export const removeUserFromPromotion = ({ promoId, userId }: any) => {
    return httpRequestAuth().delete(
        promotions.REMOVE_USER_FROM_PROMOTION.replace(':promoId', promoId.toString()).replace(':userId', '' + userId),
    );
};

export const addRestaurantToPromotion = ({ promoId, restaurantId }: any) => {
    return httpRequestAuth().put(
        promotions.ADD_RESTAURANT_TO_PROMOTION.replace(':promoId', promoId.toString()).replace(
            ':restaurantId',
            '' + restaurantId,
        ),
    );
};

export const removeRestaurantFromPromotion = ({ promoId, restaurantId }: any) => {
    return httpRequestAuth().delete(
        promotions.REMOVE_RESTAURANT_FROM_PROMOTION.replace(':promoId', promoId.toString()).replace(
            ':restaurantId',
            '' + restaurantId,
        ),
    );
};

export const fetchPromotionUsers = (params: object) => {
    return httpRequestAuth().get(promotions.PROMOTION_GET_USER_LIST, { params });
};

export const removeUser = (promoId: number, userId: number) => {
    return httpRequestAuth().delete(
        promotions.PROMOTION_REMOVE_USER.replace(':promoId', promoId.toString()).replace(':userId', userId.toString()),
    );
};

export const addUsers = ({ promotionId, userIds }: {
    promotionId: number;
    userIds: number[];
}) => {
    return httpRequestAuth().post(
        promotions.PROMOTION_ADD_USERS.replace(':promotionId', promotionId.toString()),
        {
            userIds
        },
    );
};
