import { httpRequestAuth } from './httpClient';
import { merchantRegistration } from '~/constants/apiPaths';

export const fetchList = (params: Object) => {
  return httpRequestAuth().get(merchantRegistration.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(merchantRegistration.FETCH_DETAILS.replace(':id', id.toString()));
};

export const update = (id: number, data: Object) => {
  return httpRequestAuth().patch(merchantRegistration.UPDATE.replace(':id', id.toString()), data);
};

export const remove = (id: number) => {
  return httpRequestAuth().delete(merchantRegistration.REMOVE.replace(':id', id.toString()));
};

export const checkAvailable = (params: Object) => {
  return httpRequestAuth().get(merchantRegistration.CHECK_AVAILABLE, { params });
};

export const createMerchantWithIdCard = (data: Object) => {
  return httpRequestAuth().post(merchantRegistration.CREATE_WITH_ID_CARD, data);
};
