import { httpRequestAuth } from "./httpClient";
import { restaurantShipperComplaints } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(restaurantShipperComplaints.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(restaurantShipperComplaints.FETCH_DETAILS.replace(':id', id.toString()))
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(restaurantShipperComplaints.DELETE.replace(':id', id.toString()))
}

