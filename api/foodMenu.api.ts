import { httpRequestAuth } from './httpClient';
import { foodMenu } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(foodMenu.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(foodMenu.FETCH_DETAILS.replace(':id', id.toString()));
};

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(foodMenu.UPDATE.replace(':id', id.toString()), params);
};

export const updateOrdinal = (params: object) => {
    return httpRequestAuth().put(foodMenu.UPDATE_ORDINAL, params);
};

export const create = (params: object) => {
    return httpRequestAuth().post(foodMenu.CREATE, params);
};

export const remove = (id: number) => {
    return httpRequestAuth().delete(foodMenu.DELETE.replace(':id', id.toString()));
};

export const bulkCreate = (params: object) => {
    return httpRequestAuth().post(foodMenu.BULK_CREATE, params);
};
