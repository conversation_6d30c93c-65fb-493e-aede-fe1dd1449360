import { httpRequestAuth } from "./httpClient";
import { seller } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
  return httpRequestAuth().get(seller.FETCH_SELLER_REVIEW_LIST, { params });
};


export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(seller.FETCH_SELLER_REVIEW.replace(':id', id.toString()))
};

export const fetchRatingSeller = (id: number) => {
    return httpRequestAuth().get(seller.FETCH_RATING_SELLER.replace(':id', id.toString()))
};
