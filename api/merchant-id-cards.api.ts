import { httpRequestAuth } from './httpClient';
import { MERCHANT_ID_CARDS } from '~/constants/apiPaths';

export const fetchList = (params: unknown) => {
  return httpRequestAuth().get(MERCHANT_ID_CARDS.FETCH_LIST, { params });
};

export const fetchDetail = (id: number) => {
  return httpRequestAuth().get(MERCHANT_ID_CARDS.FETCH_DETAILS.replace(':id', id.toString()));
};

export const approve = (id: number) => {
  return httpRequestAuth().post(MERCHANT_ID_CARDS.APPROVE.replace(':id', id.toString()));
};

export const reject = (id: number, reason: string) => {
  return httpRequestAuth().post(MERCHANT_ID_CARDS.REJECT.replace(':id', id.toString()), { reason });
};
