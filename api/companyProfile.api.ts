import { companyProfile } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchList = (query: Record<string, string>) => {
    return httpRequestAuth().get(companyProfile.FETCH_LIST, {
        params: query
    });
}

export const getProfile = (id: number) => {
    return httpRequestAuth().get(companyProfile.FETCH_PROFILE_DETAILS.replace(':id', id.toString()));
}

export const getProfileByMerchantId = (id: number) => {
    return httpRequestAuth().get(companyProfile.GET_PROFILE_DETAILS.replace(':id', id.toString()));
}

export const createCompanyProfile = (body: Record<string, any>) => {
    return httpRequestAuth().post(companyProfile.CREATE_PROFILE, body);
};

export const updateCompanyProfile = (id: number, body: Record<string, any>) => {
    return httpRequestAuth().put(companyProfile.UPDATE_PROFILE.replace(':id', id.toString()), body);
};

export const fetchBusinessTypes = (query: Record<string, string>) => {
    return httpRequestAuth().get(companyProfile.FETCH_BUSINESS_TYPE_LIST, {
        params: query
    });
}

export const createBusinessType = (body: Record<string, any>) => {
    return httpRequestAuth().post(companyProfile.CREATE_BUSINESS_TYPE, body);
};

export const updateBusinessType = (id: number, body: Record<string, any>) => {
    return httpRequestAuth().put(companyProfile.UPDATE_BUSINESS_TYPE.replace(':id', id.toString()), body);
};