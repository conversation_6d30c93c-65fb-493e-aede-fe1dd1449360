import { httpRequestAuth } from "./httpClient";
import { foodReviews } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(foodReviews.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(foodReviews.FETCH_DETAILS.replace(':id', id.toString()))
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(foodReviews.DELETE.replace(':id', id.toString()))
}

