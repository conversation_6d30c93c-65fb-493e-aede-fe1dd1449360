import { restaurants } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchRestaurantList = (params: object) => {
    return httpRequestAuth().get(restaurants.FETCH_RESTAURANT_LIST, {
        params: params,
    });
};

export const fetchRestaurantDetails = (id: number) => {
    return httpRequestAuth().get(restaurants.FETCH_RESTAURANT_DETAILS + id);
};

export const updateRestaurant = (id: number, params: object) => {
    return httpRequestAuth().put(restaurants.UPDATE_RESTAURANT + id, params);
};

export const updateRestaurantAdminSetting = (id: number, params: object) => {
    return httpRequestAuth().put(restaurants.UPDATE_RESTAURANT_ADMIN_SETTING.replace(':id', id.toString()), params);
};

export const updateRestaurantOntop = (id: number, params: object) => {
    return httpRequestAuth().put(restaurants.UPDATE_RESTAURANT_ONTOP.replace(':id', id.toString()), params);
};

export const reopenRestaurant = (params: object) => {
    return httpRequestAuth().post(restaurants.REOPEN_RESTAURANT, params);
};

export const createRestaurant = (params: object) => {
    return httpRequestAuth().post(restaurants.CREATE_RESTAURANT, params);
};

export const deleteRestaurant = (id: number) => {
    return httpRequestAuth().delete(restaurants.DELETE_RESTAURANT.replace(':id', id.toString()));
};

export const dashboard = (id: number, params: object) => {
    return httpRequestAuth().get(restaurants.DASHBOARD.replace(':id', id.toString()), { params });
};

export const generateCode = () => {
    return httpRequestAuth().post(restaurants.GENERATE_CODE);
};

export const rePrefixCodeRestaurant = (payload: any) => {
    return httpRequestAuth().post(restaurants.REPREFIX_CODE_RESTAURANT, payload);
};

// export const generateFirebaseDynamicLink = (id: number) => {
//     return httpRequestAuth().post(restaurants.GENERATE_FIREBASE_DYNAMIC_LINK.replace(':id', id.toString()));
// };

export const getMerchantByRestaurantId = (restaurantId: number) => {
    return httpRequestAuth().get(restaurants.FETCH_MERCHANT_BY_RESTAURANT_ID.replace(':id', restaurantId.toString()));
};

export const getRestaurantByMerchant = (params: object) => {
    return httpRequestAuth().get(restaurants.FETCH_RESTAURANT_BY_MERCHANT, { params: params });
};

export const updateMerchantForRestaurant = (restaurantId: number, merchantId: number) => {
    return httpRequestAuth().post(
        restaurants.UPDATE_MERCHANT_BY_RESTAURANT_ID.replace(':restaurantId', restaurantId.toString()).replace(
            ':merchantId',
            merchantId.toString(),
        ),
    );
};
export const updateMerchantRoleOfRestaurant = (
    restaurantId: number,
    merchantId: number,
    roleId: number,
    isActive: number,
) => {
    return httpRequestAuth().put(
        restaurants.UPDATE_MERCHANT_ROLE_OF_RESTAURANT.replace(':restaurantId', restaurantId.toString()).replace(
            ':merchantId',
            merchantId.toString(),
        ),
        { roleId, isActive },
    );
};

export const deleteMerchantFromRestaurant = (restaurantId: number, merchantId: number) => {
    return httpRequestAuth().delete(
        restaurants.DELETE_MERCHANT_FROM_RESTAURANT.replace(':restaurantId', restaurantId.toString()).replace(
            ':merchantId',
            merchantId.toString(),
        ),
    );
};
export const fetchRestaurantFilterSearch = () => {
    return httpRequestAuth().get(restaurants.FETCH_RESTAURANT_FILTER_SEARCH);
};

export const fetchRestaurantStats = (restaurantId: number) => {
    return httpRequestAuth().get(restaurants.FETCH_RESTAURANT_STATS.replace(':id', restaurantId.toString()));
};

export const fetchInComStatementRestaurant = (id: number, params: object) => {
    return httpRequestAuth().get(restaurants.FETCH_RESTAURANT_INCOM_STATEMENT.replace(':id', id.toString()), {
        params,
    });
};

export const reAggregateTotalReviewsAndRating = (id: number) => {
    return httpRequestAuth().put(restaurants.RE_AGGREGATE_REVIEW.replace(':id', id.toString()));
};

export const revenue = (id: number, params: object) => {
    return httpRequestAuth().get(restaurants.REVENUE.replace(':id', id.toString()), { params });
};

export const addSeller = (id: number, seller_id: number) => {
    return httpRequestAuth().post(
        restaurants.UPDATE_SELLER_FOR_RESTAURANT.replace(':restaurantId', id.toString()).replace(
            ':sellerId',
            seller_id.toString(),
        ),
    );
};

export const deleteSeller = (id: number) => {
    return httpRequestAuth().delete(restaurants.DELETE_SELLER.replace(':restaurantId', id.toString()));
};

export const getSeller = (id: number) => {
    return httpRequestAuth().get(restaurants.FETCH_SELLER.replace(':restaurantId', id.toString()));
};

export const updateSellerForRestaurants = (from: number, to: number) => {
    return httpRequestAuth().put(
        restaurants.CHANGE_SELLER_OF_RESTAURANTS.replace(':from', from.toString()).replace(':to', to.toString()),
    );
};

export const updateBranchForRestaurants = (id: number) => {
    return httpRequestAuth().put(restaurants.UPDATE_BRANCH_FOR_RESTAURANT.replace(':restaurantId', id.toString()));
};

export const generateRestaurantDescription = (queries: object) => {
    return httpRequestAuth().get(restaurants.GENERATE_RESTAURANT_DESCRIPTION, { params: queries });
};

export const getListWithRevenue = (params: object) => {
    return httpRequestAuth().get(restaurants.GET_LIST_WITH_REVENUE, { params });
};

export const reindexRestaurantPromotions = (id: number) => {
    return httpRequestAuth().post(restaurants.REINDEX_RESTAURANT_PROMOTIONS, { restaurantId: id });
};

export const getRestaurantsHaveNoRevenue = (params: object) => {
    return httpRequestAuth().get(restaurants.FETCH_EXPORT_RESTAURANTS_HAVE_NO_REVENUE, { params });
};
