import { location } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchLocationList = async () => {
    return await httpRequestAuth().get(location.FETCH_LIST);
};

export const fetchLocationDetails = (id: number) => {
    return httpRequestAuth().get(location.FETCH_DETAILS.replace(':id', id.toString()));
};

export const fetchLocationListByParentId = (id: number | null) => {
    return httpRequestAuth().get(location.FETCH_LIST_BY_PARENT_ID.replace(':id', id?.toString() || ''));
};

export const createLocation = (payload: any) => {
    return httpRequestAuth().post(location.CREATE_LOCATION, payload);
};

export const mergeWard = (payload: any) => {
    return httpRequestAuth().post(location.MERGE_WARD, payload);
};

export const fetchPreviewLocation = () => {
    return httpRequestAuth().get(location.PREVIEW_LOCATION);
};

export const fetchNewLocationList = async () => {
    return await httpRequestAuth().get(location.FETCH_NEW_LIST);
};

export const fetchWardsByProvinceDistrict = (params: {
    provinceId: number;
    districtId: number;
    page?: number;
    limit?: number;
}) => {
    return httpRequestAuth().get(location.FETCH_WARDS_BY_PROVINCE_DISTRICT, {
        params,
    });
};

export const deleteLocation = (id: number) => {
    return httpRequestAuth().delete(location.FETCH_DETAILS.replace(':id', id.toString()));
};
