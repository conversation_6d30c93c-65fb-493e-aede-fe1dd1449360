import { httpRequestAuth } from "./httpClient";
import { EMPLOYEE_PERMISSIONS } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
  return httpRequestAuth().get(EMPLOYEE_PERMISSIONS.FETCH_LIST, { params });
};

export const fetchAll = () => {
  return httpRequestAuth().get(EMPLOYEE_PERMISSIONS.FETCH_ALL);
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    EMPLOYEE_PERMISSIONS.FETCH_DETAILS.replace(":id", id.toString())
  );
};

export const update = (id: number, params: object) => {
  return httpRequestAuth().put(
    EMPLOYEE_PERMISSIONS.UPDATE.replace(":id", id.toString()),
    params
  );
};

export const create = (params: object) => {
  return httpRequestAuth().post(<PERSON>MPLOYEE_PERMISSIONS.CREATE, params);
};

export const remove = (id: number) => {
  return httpRequestAuth().delete(
    EMPLOYEE_PERMISSIONS.DELETE.replace(":id", id.toString())
  );
};
