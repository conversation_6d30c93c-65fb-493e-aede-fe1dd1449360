import { httpRequestAuth } from './httpClient';
import { RESTAURANT_PAYMENT_METHOD } from '~/constants/apiPaths';

export const fetchList = (restaurantId: number) => {
    return httpRequestAuth().get(RESTAURANT_PAYMENT_METHOD.FETCH.replace(':id', restaurantId.toString()));
};

export const update = (restaurantId: number, paymentMethods: number[]) => {
    return httpRequestAuth().patch(RESTAURANT_PAYMENT_METHOD.UPDATE.replace(':id', restaurantId.toString()), {
        paymentMethodIds: paymentMethods,
    });
};
