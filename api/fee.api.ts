import { httpRequestAuth } from './httpClient';
import { fee } from '~/constants/apiPaths';

export const fetchList = () => {
    return httpRequestAuth().get(fee.FETCH_LIST);
};

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(fee.UPDATE.replace(':id', id.toString()), params);
};

export const create = (params: object) => {
    return httpRequestAuth().post(fee.CREATE, params);
};

export const deleteById = (id: number) => {
    return httpRequestAuth().delete(fee.DELETE.replace(':id', id.toString()));
}
