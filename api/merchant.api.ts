import { merchants } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const createOnepayCollectUser = (id: number) => {
    return httpRequestAuth().post(merchants.CREATE_ONEPAY_COLLECT_USER.replace(':id', id.toString()));
};

export const getOnePayCollectUser = (id: number) => {
    return httpRequestAuth().get(merchants.GET_ONEPAY_COLLECT_USER.replace(':id', id.toString()));
};

export const updateOnePayCollectUser = (id: number, params: Record<string, any>) => {
    return httpRequestAuth().put(merchants.UPDATE_ONEPAY_COLLECT_USER.replace(':id', id.toString()), params);
};

export const getBankAccounts = (id: number) => {
    return httpRequestAuth().get(merchants.GET_BANK_ACCOUNTS.replace(':id', id.toString()));
};

export const createBankAccount = (merchantId: number, payload: Record<string, any>) => {
    return httpRequestAuth().post(merchants.CREATE_BANK_ACCOUNT.replace(':id', merchantId.toString()), payload);
};

export const updateBankAccount = (merchantId: number, bankAccountId: number, params: Record<string, any>) => {
    return httpRequestAuth().put(
        merchants.UPDATE_BANK_ACCOUNT.replace(':id', merchantId.toString()).replace(
            ':bankAccountId',
            bankAccountId.toString(),
        ),
        params,
    );
};

export const getWalletByMerchantId = (id: number) => {
    return httpRequestAuth().get(merchants.GET_WALLLET.replace(':id', id.toString()));
};

export const createWalletByMerchantId = (merchantId: number) => {
    return httpRequestAuth().post(merchants.CREATE_WALLLET.replace(':id', merchantId.toString()));
};

export const getBalanceTransactionsWithPagination = (merchantId: number, params: Record<string, any>) => {
    return httpRequestAuth().get(
        merchants.GET_BALANCE_TRANSACTION_WITH_PAGINATION.replace(':id', merchantId.toString()),
        {
            params,
        },
    );
};

export const getOrderTransactionsWithPagination = (merchantId: number, params: Record<string, any>) => {
    return httpRequestAuth().get(
        merchants.GET_ORDER_TRANSACTION_WITH_PAGINATION.replace(':id', merchantId.toString()),
        {
            params,
        },
    );
};

export const getChargeTransactionsWithPagination = (merchantId: number, params: Record<string, any>) => {
    return httpRequestAuth().get(
        merchants.GET_CHARGE_TRANSACTION_WITH_PAGINATION.replace(':id', merchantId.toString()),
        {
            params,
        },
    );
};

export const getPayoutTransactionsWithPagination = (merchantId: number, params: Record<string, any>) => {
    return httpRequestAuth().get(
        merchants.GET_PAYOUT_TRANSACTION_WITH_PAGINATION.replace(':id', merchantId.toString()),
        {
            params,
        },
    );
};

export const getRefundApplicationFeeWithPagination = (merchantId: number, params: Record<string, any>) => {
    return httpRequestAuth().get(
        merchants.GET_REFUND_APPLICATION_FEE_WITH_PAGINATION.replace(':id', merchantId.toString()),
        {
            params,
        },
    );
};

export const getMerchantListWithPagination = (params: Record<string, any>) => {
    return httpRequestAuth().get(merchants.GET_MERCHANT_LIST, {
        params,
    });
};

export const ADD_MERCHANT_TO_RESTAURANT = (merchantId: number, restaurantId: number) => {
    return httpRequestAuth().post(
        merchants.ADD_MERCHANT_TO_RESTAURANT.replace(':merchantId', merchantId.toString()).replace(
            ':restaurantId',
            restaurantId.toString(),
        ),
    );
};

export const REMOVE_MERCHANT_FROM_RESTAURANT = (merchantId: number, restaurantId: number) => {
    return httpRequestAuth().delete(
        merchants.REMOVE_MERCHANT_FROM_RESTAURANT.replace(':merchantId', merchantId.toString()).replace(
            ':restaurantId',
            restaurantId.toString(),
        ),
    );
};

export const getMerchantProfile = (id: number) => {
    return httpRequestAuth().get(merchants.GET_MERCHANT_DETAILS.replace(':id', id.toString()));
};

export const updateMerchantProfile = (merchantId: number, body: Record<string, any>) => {
    return httpRequestAuth().put(merchants.GET_MERCHANT_DETAILS.replace(':id', merchantId.toString()), body);
};

export const createMerchant = (body: Record<string, any>) => {
    return httpRequestAuth().post(merchants.CREATE_MERCHANT, body);
};

export const createPayoutTransaction = (id: number, body: Record<string, any>) => {
    return httpRequestAuth().post(merchants.CREATE_PAYOUT_TRANSACTION.replace(':id', id.toString()), body);
};

export const getMerchantIdCard = (id: number) => {
    return httpRequestAuth().get(merchants.GET_MERCHANT_ID_CARD.replace(':id', id.toString()));
};

export const createMerchantIdCard = (id: number, body: Record<string, any>) => {
    return httpRequestAuth().post(merchants.CREATE_MERCHANT_ID_CARD.replace(':id', id.toString()), body);
};

export const updateMerchantIdCard = (id: number, body: Record<string, any>) => {
    return httpRequestAuth().patch(merchants.UPDATE_MERCHANT_ID_CARD.replace(':id', id.toString()), body);
};

export const getKeyImages = () => {
    return httpRequestAuth().get(merchants.GET_KEY_IMAGES);
};

export const getAlreadyPresignedUrls = (merchantId: number) => {
    return httpRequestAuth().get(merchants.GET_ALREADY_PRESIGNED_URLS.replace(':merchantId', merchantId.toString()));
};

export const presignedUrl = (id: number) => {
    return httpRequestAuth().get(merchants.CHANGE_MERCHANT_PHOTO.replace(':id', id.toString()));
};

export const fetchRestaurantList = (params: number) => {
    return httpRequestAuth().get(merchants.GET_RESTAURANTS_OF_MERCHANT.replace(':id', params.toString()));
};

export const create9payCollectUser = (id: number, body: Record<string, any>) => {
    return httpRequestAuth().post(merchants.CREATE_9PAY_COLLECT_USER.replace(':id', id.toString()), body);
};

export const get9payCollectUser = (id: number) => {
    return httpRequestAuth().get(merchants.GET_9PAY_COLLECT_USER.replace(':id', id.toString()));
};
export const get9PayCollectSupportBanks = () => {
    return httpRequestAuth().get(merchants.GET_9PAY_COLLECT_SUPPORT_BANKS);
};
