import { globalRestaurant } from "~/constants/apiPaths";
import { httpRequestAuth } from "./httpClient";

export const fetchList = (params: object) => {
  return httpRequestAuth().get(globalRestaurant.FETCH_RESTAURANTS, {params})
}

export const fetchRestaurantsBySeller = (id: number, params: object) => {
  return httpRequestAuth().get(
    globalRestaurant.FETCH_RESTAURANTS_BY_SELLER.replace(":sellerId", id.toString()), {params}
  );
};

