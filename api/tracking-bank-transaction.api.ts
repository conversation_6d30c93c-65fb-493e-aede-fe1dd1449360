import { httpRequestAuth } from './httpClient';
import { DRIVER_AGREEMENT_FOR_ADMIN, TRACKING_BANK_TRANSACTION } from '~/constants/apiPaths';
import { 
  TrackingBankTransactionQueryParams,
  BankAccountUpdateParams,
  TransactionUpdateParams
} from '@/types/archive/tracking-bank-transaction';

export const fetchList = (params: TrackingBankTransactionQueryParams) => {
  return httpRequestAuth().get(TRACKING_BANK_TRANSACTION.FETCH_LIST, { params });
};

export const fetchBankAccounts = (params?: unknown) => {
  return httpRequestAuth().get(TRACKING_BANK_TRANSACTION.FETCH_BANK_ACCOUNTS, { params });
};

export const updateBankAccount = (id: number, params: BankAccountUpdateParams) => {
  return httpRequestAuth().put(TRACKING_BANK_TRANSACTION.UPDATE_BANK_ACCOUNT.replace(':id', id.toString()), params);
};

export const updateTransaction = (id: number, params: TransactionUpdateParams) => {
  return httpRequestAuth().put(TRACKING_BANK_TRANSACTION.UPDATE_TRANSACTION.replace(':id', id.toString()), params);
};

export const update = (id: number) => {
  return httpRequestAuth().patch(DRIVER_AGREEMENT_FOR_ADMIN.UPDATE.replace(':id', id.toString()));
};

export const fetchDetail = (id: number) => {
  return httpRequestAuth().get(DRIVER_AGREEMENT_FOR_ADMIN.FETCH_DETAILS.replace(':id', id.toString()));
};

export const approve = (id: number) => {
  return httpRequestAuth().post(DRIVER_AGREEMENT_FOR_ADMIN.APPROVE.replace(':id', id.toString()));
};

export const reject = (id: number, reason: string) => {
  return httpRequestAuth().post(DRIVER_AGREEMENT_FOR_ADMIN.REJECT.replace(':id', id.toString()), { reason });
};

export const remove = (id: number) => {
  return httpRequestAuth().delete(DRIVER_AGREEMENT_FOR_ADMIN.REMOVE.replace(':id', id.toString()));
};

export const reGenerate = (id: number) => {
  return httpRequestAuth().post(DRIVER_AGREEMENT_FOR_ADMIN.RE_GENERATE.replace(':id', id.toString()));
};

export const getStatistics = (id: number) => {
  return httpRequestAuth().get(DRIVER_AGREEMENT_FOR_ADMIN.GET_STATISTICS.replace(':id', id.toString()));
};

export const reGenerateForNewDriver = (id: number) => {
  return httpRequestAuth().post(DRIVER_AGREEMENT_FOR_ADMIN.RE_GENERATE_NEW_DRIVER.replace(':id', id.toString()));
}