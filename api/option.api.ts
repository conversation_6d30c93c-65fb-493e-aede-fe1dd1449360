import { httpRequestAuth } from "./httpClient";
import { options } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(options.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(options.FETCH_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(options.UPDATE.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(options.CREATE, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(options.DELETE.replace(':id', id.toString()))
}