import { ORDER_COMPLETED_STATS } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(ORDER_COMPLETED_STATS.FETCH_LIST, { params });
};

export const fetchTargetConstants = (params: object) => {
    return httpRequestAuth().get(ORDER_COMPLETED_STATS.FETCH_TARGET_CONSTANTS, { params });
};

export const updateTargetConstants = (params: object) => {
    return httpRequestAuth().put(ORDER_COMPLETED_STATS.UPDATE_TARGET_CONSTANTS, params);
};

export const fetchTenantGroups = () => {
    return httpRequestAuth().get(ORDER_COMPLETED_STATS.FETCH_TENANT_GROUPS);
};

export const updateTenantGroups = (params: object) => {
    return httpRequestAuth().put(ORDER_COMPLETED_STATS.UPDATE_TENANT_GROUPS, params);
};

export const fetchStats = (params: object) => {
    return httpRequestAuth().get(ORDER_COMPLETED_STATS.FETCH_STATS, { params });
};

export const updateStats = (params: object) => {
    return httpRequestAuth().patch(ORDER_COMPLETED_STATS.UPDATE_STATS, params);
};

export const removeStats = (params: object) => {
    return httpRequestAuth().delete(ORDER_COMPLETED_STATS.REMOVE_STATS, {
        data: params,
    });
};

export const fetchStatuses = (params: object) => {
    return httpRequestAuth().get(ORDER_COMPLETED_STATS.FETCH_STATUSES, { params });
};

export const fetchCancelList = (params: object) => {
    return httpRequestAuth().get(ORDER_COMPLETED_STATS.FETCH_CANCEL_LIST, { params });
};

export const fetchCancelStats = (params: object) => {
    return httpRequestAuth().get(ORDER_COMPLETED_STATS.FETCH_CANCEL_STATS, { params });
};