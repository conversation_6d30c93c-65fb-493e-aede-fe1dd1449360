import { httpRequestAuth } from "./httpClient";
import { archiveBatchJob } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth({
        // // DOMAIN: 'http://localhost:3000'
    }).get(archiveBatchJob.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth({
        // // DOMAIN: 'http://localhost:3000'
    }).get(archiveBatchJob.FETCH_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth({
        // // DOMAIN: 'http://localhost:3000'
    }).put(archiveBatchJob.UPDATE.replace(':id', id.toString()), params)
}

export const create = (body: object) => {
    return httpRequestAuth({
        // // DOMAIN: 'http://localhost:3000'
    }).post(archiveBatchJob.CREATE, body)
}
