import { httpRequestAuth } from "./httpClient";
import { faqs } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(faqs.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(faqs.FETCH_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(faqs.UPDATE.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(faqs.CREATE, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(faqs.DELETE.replace(':id', id.toString()))
}