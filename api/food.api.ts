import { httpRequestAuth } from './httpClient';
import { foods } from '~/constants/apiPaths';

export const fetchFoodList = (params: object) => {
    return httpRequestAuth().get(foods.FETCH_FOOD_LIST, {
        params: params,
    });
};

export const fetchFoodDetails = (id: number) => {
    return httpRequestAuth().get(foods.FETCH_FOOD_DETAILS + id);
};

export const create = (params: object) => {
    return httpRequestAuth().post(foods.CREATE_FOOD, params);
};

export const updateFood = (id: number, params: object) => {
    return httpRequestAuth().put(foods.UPDATE_FOOD + id, params);
};

export const remove = (id: number) => {
    return httpRequestAuth().delete(foods.DELETE.replace(':id', id.toString()));
};

export const uploadImage = (id: number, params: object) => {
    return httpRequestAuth().post(
        foods.UPLOAD_IMAGE.replace(':id', id.toString()),
        { params },
        {
            headers: {},
        },
    );
};

export const getSalesLimitManagement = (id: number) => {
    return httpRequestAuth().get(foods.GET_FOOD_SALES_LIMIT.replace(':id', id.toString()));
};

export const updateSalesLimitManagement = (id: number, body: Record<string, any>) => {
    return httpRequestAuth().put(foods.GET_FOOD_SALES_LIMIT.replace(':id', id.toString()), body);
};

export const bulkCreateFoods = (params: object) => {
    return httpRequestAuth().post(foods.BULK_CREATE_FOOD, params);
};

export const generateFoodDescription = (id: number) => {
    return httpRequestAuth().patch(foods.GENERATE_FOOD_DESCRIPTION.replace(':id', id.toString()));
};
