import { httpRequestAuth } from "./httpClient";
import { galleries } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(galleries.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(galleries.FETCH_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(galleries.UPDATE.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(galleries.CREATE, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(galleries.DELETE.replace(':id', id.toString()))
}
