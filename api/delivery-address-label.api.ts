import { httpRequestAuth } from './httpClient';
import { DELIVERY_ADDRESS_LABEL } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(DELIVERY_ADDRESS_LABEL.FETCH_LIST, { params });
};

export const create = (params: object) => {
    return httpRequestAuth().post(DELIVERY_ADDRESS_LABEL.CREATE, params);
};

export const remove = (id: number) => {
    return httpRequestAuth().delete(DELIVERY_ADDRESS_LABEL.DELETE.replace(':id', id.toString()));
};

export const update = (params: any) => {
    return httpRequestAuth().put(DELIVERY_ADDRESS_LABEL.UPDATE.replace(':id', params.id), { title: params.title });
};
