import { httpRequestAuth } from './httpClient';
import { USER_ORDERS_RATING } from '~/constants/apiPaths';

export const fetchList = (params: Record<string, any>) => {
    return httpRequestAuth().get(USER_ORDERS_RATING.FETCH_LIST, {
        params,
    });
};

export const deleteUserOrdersRate = (userId: string) => {
    return httpRequestAuth().delete(USER_ORDERS_RATING.DELETE.replace(':id', userId));
};

export const fetchDetail = (params: Record<string, any>) => {
    return httpRequestAuth().get(USER_ORDERS_RATING.FETCH_DETAILS.replace(':id', params.user_id), {
        params,
    });
};

export const updateUsersOrdersRate = (userId: string) => {
    return httpRequestAuth().patch(USER_ORDERS_RATING.UPDATE.replace(':id', userId));
};
