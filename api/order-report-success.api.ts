import { ORDER_SUCCESS_STATISTICS } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(ORDER_SUCCESS_STATISTICS.FETCH_LIST, { params });
};

export const update = (params: object) => {
    return httpRequestAuth().put(ORDER_SUCCESS_STATISTICS.UPDATE, params);
};

export const fetchTargetConstants = (params: object) => {
    return httpRequestAuth().get(ORDER_SUCCESS_STATISTICS.FETCH_TARGET_CONSTANTS, { params });
};

export const updateTargetConstants = (params: object) => {
    return httpRequestAuth().put(ORDER_SUCCESS_STATISTICS.UPDATE_TARGET_CONSTANTS, params);
};
