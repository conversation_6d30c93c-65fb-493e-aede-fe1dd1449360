import { httpRequestAuth } from "./httpClient";
import { ORDER_DRIVER_EXPENSE } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().post(ORDER_DRIVER_EXPENSE.FETCH_LIST, params);
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(ORDER_DRIVER_EXPENSE.FETCH_DETAILS.replace(':id', id.toString()));
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(ORDER_DRIVER_EXPENSE.UPDATE.replace(':id', id.toString()), params);
}

export const create = (params: object) => {
    return httpRequestAuth().post(ORDER_DRIVER_EXPENSE.CREATE, params);
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(ORDER_DRIVER_EXPENSE.DELETE.replace(':id', id.toString()));
}

export const exportGetList = (params: object) => {
    return httpRequestAuth().post(ORDER_DRIVER_EXPENSE.EXPORT_GET_LIST, params);
}

// Keep the original function for backward compatibility
export const fetchOrderDriverExpenseList = (params: object) => {
    return fetchList(params);
}
