import { DRIVER_TAX_REPORTING } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';
import { IHttpResponse, IPagination } from '~/types';
import { IDriverTaxReport } from '~/types/driverTaxReport.interface';

export const fetchDriverList = (id: number, params: object) => {
    return httpRequestAuth().post(DRIVER_TAX_REPORTING.GET_LIST, {
        driver_id: id,
        ...params,
    });
};

export const fetchList = (params: object) => {
    return httpRequestAuth().post(DRIVER_TAX_REPORTING.GET_LIST, params);
};

export const create = (body: Record<string, any>) => {
    return httpRequestAuth().post(DRIVER_TAX_REPORTING.CREATE, body);
};

export const update = (id: number, body: object) => {
    return httpRequestAuth().post(DRIVER_TAX_REPORTING.UPDATE.replace(':id', id.toString()), body);
};

export const aggTaxReports = (body: object) => {
    return httpRequestAuth().post(DRIVER_TAX_REPORTING.AGG_TAX_REPORTS, body);
};

export const exportGetList = (body: object) => {
    return httpRequestAuth().post<IHttpResponse<IPagination<IDriverTaxReport>>>(
        DRIVER_TAX_REPORTING.EXPORT_GET_LIST,
        body,
    );
};

export const getStats = (payload: any) => {
    return httpRequestAuth().post(DRIVER_TAX_REPORTING.GET_STATS, payload);
};

export const getStatsList = (payload: any) => {
    return httpRequestAuth().post(DRIVER_TAX_REPORTING.GET_STATS_LIST, payload);
};

export const importData = (payload: any) => {
    return httpRequestAuth().post(DRIVER_TAX_REPORTING.IMPORT, payload, {
        timeout: 1000 * 60 * 10, // 10 minutes
    });
};

export const aggregateAllIncome = (payload: any) => {
    return httpRequestAuth().post(DRIVER_TAX_REPORTING.AGGREGATE_ALL_INCOME, payload, {
        timeout: 1000 * 60 * 10, // 10 minutes
    });
};

export const getMonthlySummary = (payload: any) => {
    return httpRequestAuth().post(DRIVER_TAX_REPORTING.GET_MONTHLY_SUMMARY, payload);
};


export const finalizeMonthTaxReports = (payload: any) => {
    return httpRequestAuth().post(DRIVER_TAX_REPORTING.FINALIZE_MONTH_TAX_REPORT, payload);
};


