import { merchantLocation } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchMerchantLocation = (merchantId: number) => {
    return httpRequestAuth().get(merchantLocation.FETCH, {
        params: {
            merchant_id: merchantId,
        },
    });
};

export const updateMerchantLocation = (params: object) => {
    return httpRequestAuth().post(merchantLocation.UPDATE, params);
};
