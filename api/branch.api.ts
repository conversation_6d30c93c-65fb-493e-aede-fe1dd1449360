import { httpRequestAuth } from "./httpClient";
import { branches } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(branches.FETCH_BRANCH_LIST, {params});
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(branches.FETCH_BRANCH_DETAILS.replace(':branchId', id.toString()));
}

export const createBranch = (params: object) => {
    return httpRequestAuth().post(branches.CREATE_BRANCH, params);
}

export const updateBranch = (id: number, params: object) => {
    return httpRequestAuth().put(branches.UPDATE_BRANCH.replace(':branchId', id.toString()), params);
}

export const deleteBranch = (id: number) => {
    return httpRequestAuth().delete(branches.DELETE_BRANCH.replace(':branchId', id.toString()));
}