import { archiveOrder } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(archiveOrder.FETCH_LIST, { params });
};

export const fetchDetails = (id: number, params: object) => {
    return httpRequestAuth().get(archiveOrder.FETCH_DETAILS.replace(':id', id.toString()), {
        params,
    });
};
