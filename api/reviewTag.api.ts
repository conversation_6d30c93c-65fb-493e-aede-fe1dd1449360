import { httpRequestAuth } from "./httpClient";
import { reviewTags } from "~/constants/apiPaths";

export const fetchList = () => {
  return httpRequestAuth().get(reviewTags.FETCH_LIST);
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    reviewTags.FETCH_DETAILS.replace(":id", id.toString())
  );
};

export const createTag = (params: Object) => {
  return httpRequestAuth().post(reviewTags.CREATE, params);
};

export const updateTag = (id: number, params: Object) => {
  return httpRequestAuth().put(
    reviewTags.UPDATE.replace(":id", id.toString()),
    params
  );
};

export const deleteTag = (id: number) => {
  return httpRequestAuth().delete(
    reviewTags.DELETE.replace(":id", id.toString())
  );
};
