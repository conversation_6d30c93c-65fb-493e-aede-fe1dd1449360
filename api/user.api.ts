import { httpRequest, httpRequestAuth } from './httpClient';
import { users } from '~/constants/apiPaths';
import { getAppConfigs } from '~/configs';
import { getProvinceIdStorage } from '~/utils/common';

export const fetchUserList = (params: object, provinceId?: string) => {
    let xProvince = provinceId;
    if (!provinceId) {
        xProvince = getProvinceIdStorage();
    }
    return httpRequestAuth({ headers: { 'X-Province': xProvince } }).get(users.FETCH_USER_LIST, {
        params: params,
    });
};

export const fetchUserListV2 = (params: object) => {
    return httpRequestAuth().get(users.FETCH_USER_LIST_V2, params);
};

export const fetchSellerList = (params: object) => {
    return httpRequestAuth().get(users.FETCH_SELLER_LIST, {
        params: params,
    });
};

export const fetchDriverList = (params: object) => {
    return httpRequestAuth().get(users.FETCH_DRIVER_LIST, {
        params: params,
    });
};

export const fetchDriverListV2 = (params: object) => {
    return httpRequestAuth().get(users.FETCH_DRIVER_LIST_V2, {
        params: params,
    });
};

export const fetchManagerList = (params: object) => {
    return httpRequestAuth().get(users.FETCH_MANAGER_LIST, {
        params: params,
    });
};
export const fetchAdminList = (params: object) => {
    return httpRequestAuth().get(users.FETCH_ADMIN_LIST, {
        params: params,
    });
};

export const fetchOnlineDriverList = () => {
    const { API_DOMAIN } = getAppConfigs();
    return httpRequestAuth({ DOMAIN: API_DOMAIN }).get(users.FETCH_ONLINE_DRIVER_LIST);
};

export const fetchAllShippers = (params: object) => {
    const { APP_DOMAIN } = getAppConfigs();
    return httpRequestAuth({
        DOMAIN: APP_DOMAIN,
    }).get(users.FETCH_ALL_SHIPPERS, { params });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(users.FETCH_USER_DETAILS.replace(':id', id.toString()));
};

export const fetchUserProfile = () => {
    return httpRequestAuth().get(users.FETCH_USER_PROFILE);
};

export const fetchEmployeeProfile = () => {
    return httpRequestAuth().get(users.FETCH_USER_ADMIN_PROFILE);
};

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(users.UPDATE.replace(':id', id.toString()), params);
};

export const updateAdminUser = (id: number, params: object) => {
    return httpRequestAuth().put(users.UPDATE_ADMIN_USER.replace(':id', id.toString()), params);
};

export const create = (params: object) => {
    return httpRequestAuth().post(users.CREATE, params);
};

export const createAdmin = (params: object) => {
    return httpRequestAuth().post(users.CREATE_ADMIN_USER, params);
};

export const createDriverUser = (params: object) => {
    return httpRequestAuth().post(users.CREATE_DRIVER_USER, params);
};

export const createManagerUser = (params: object) => {
    return httpRequestAuth().post(users.CREATE_MANAGER_USER, params);
};

export const login = (params: object) => {
    return httpRequest().post(users.SIGN_IN, params);
};

export const updateShipper = (id: number, params: object) => {
    return httpRequestAuth().put(users.UPDATE_DRIVER.replace(':id', id.toString()), params);
};

export const updateShipperRanking = (id: number, params: object) => {
    return httpRequestAuth().put(users.UPDATE_DRIVER_RANKING.replace(':id', id.toString()), params);
};

export const createShipper = (userId: number, payload: any) => {
    return httpRequestAuth().post(users.CREATE_DRIVER.replace(':userId', userId.toString()), payload);
};

export const deleteDriver = (userId: number, driverId: number) => {
    return httpRequestAuth().delete(
        users.DELETE_DRIVER.replace(':userId', userId.toString()).replace(':driverId', driverId.toString()),
    );
};

export const renewToken = (refreshToken: object) => {
    return httpRequest().post(users.RENEW_TOKEN, refreshToken);
};

export const lockUser = (params: object) => {
    return httpRequestAuth().post(users.LOCK_USER, params);
};

export const fetchOrderPendingDrivers = () => {
    return httpRequestAuth().get(users.FETCH_ORDER_PENDING_DRIVER);
};

export const fetchDetailsByPhone = (phone: string) => {
    return httpRequestAuth().get(users.FETCH_USER_DETAILS_BY_PHONE.replace(':phone', phone));
};

export const searchUser = (params: object) => {
    return httpRequestAuth().get(users.SEARCH, { params });
};

export const fetchDriverUser = (id: number) => {
    return httpRequestAuth().get(users.FETCH_DRIVER_USER_DETAILS.replace(':id', id.toString()));
};
