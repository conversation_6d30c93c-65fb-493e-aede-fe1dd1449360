import { archiveDatabaseConfiguration } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchAll = () => {
    return httpRequestAuth({
        // // DOMAIN: 'http://localhost:3000',
    }).get(archiveDatabaseConfiguration.FETCH_ALL);
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth({
        // // DOMAIN: 'http://localhost:3000',
    }).get(archiveDatabaseConfiguration.FETCH_DETAILS.replace(':id', id.toString()));
};

export const update = (id: number, params: object) => {
    return httpRequestAuth({
        // // DOMAIN: 'http://localhost:3000',
    }).put(archiveDatabaseConfiguration.UPDATE.replace(':id', id.toString()), params);
};

export const create = (params: object) => {
    return httpRequestAuth({
        // // DOMAIN: 'http://localhost:3000',
    }).post(archiveDatabaseConfiguration.CREATE, params);
};
