import { httpRequestAuth } from './httpClient';
import { USER_ADMIN } from '~/constants/apiPaths';

export const fetchEmployeeList = (params: Record<string, any>) => {
    return httpRequestAuth().get(USER_ADMIN.FETCH_LIST, {
        params,
    });
};

export const createEmployee = (params: Record<string, any>) => {
    return httpRequestAuth().post(USER_ADMIN.CREATE, params);
};

export const fetchEmployee = (id: string) => {
    return httpRequestAuth().get(USER_ADMIN.FETCH_DETAILS.replace(':id', id));
};

export const lockEmployee = (params: Record<string, any>) => {
    return httpRequestAuth().patch(USER_ADMIN.LOCK, params);
};

export const updateEmployee = (params: Record<string, any>) => {
    console.log('params', params);
    return httpRequestAuth().patch(USER_ADMIN.UPDATE.replace(':id', params.id), params);
};

export const fetchSellerList = (params: Record<string, any>) => {
    console.log('params', params);
    return httpRequestAuth().get(USER_ADMIN.FETCH_SELLER_LIST, {
        params,
    });
}

export const fetchByIds = (params: Record<string, any>) => {
    return httpRequestAuth().post(USER_ADMIN.FETCH_BY_IDS, params);
}