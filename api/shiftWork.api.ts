import { httpRequestAuth } from "./httpClient";
import { shiftWork } from "~/constants/apiPaths";

export const fetchList = (params: Record<string, any>) => {
  return httpRequestAuth().get(shiftWork.FETCH_LIST, { params });
}

export const create = (params: Record<string, any>) => {
  return httpRequestAuth().post(shiftWork.CREATE, params);
}

export const updateById = (id: number, body: Record<string, any>) => {
  return httpRequestAuth().put(shiftWork.UPDATE.replace(':id', id.toString()), body)
}

export const remove = (id: number) => {
  return httpRequestAuth().delete(shiftWork.DELETE.replace(':id', id.toString()))
}

export const findById = (id: number, params: Record<string, any>) => {
  return httpRequestAuth().get(shiftWork.FETCH_DETAILS.replace(':id', id.toString()), {params})
}