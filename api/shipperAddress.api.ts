import { httpRequestAuth } from "./httpClient";
import { shipperReviews } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(shipperReviews.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(shipperReviews.FETCH_DETAILS.replace(':id', id.toString()))
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(shipperReviews.DELETE.replace(':id', id.toString()))
}

