import { fraudCategory } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';
export const fetchList = (params: any) => {
    return httpRequestAuth().get(fraudCategory.FETCH_LIST, { params });
};
export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(fraudCategory.FETCH_DETAIL.replace(':id', id.toString()));
};

export const update = (id: number, body: any) => {
    return httpRequestAuth().put(fraudCategory.UPDATE.replace(':id', id.toString()), body);
};
