import { chat } from './../constants/apiPaths';
import { httpRequestAuth } from "./httpClient";

export const fetchMessageTemplateList = (params: object) => {
    return httpRequestAuth().get(chat.FETCH_MESSAGE_TEMPLATE_LIST, {params})
}

export const fetchRoomChatList = (params: object) => {
    return httpRequestAuth().get(chat.FETCH_ROOM_LIST, {params})
}

export const fetchRoomChatDetail = (roomId: string) => {
    return httpRequestAuth().get(chat.FETCH_ROOM_DETAILS.replace(':id', roomId))
}

export const fetchMessageList = (roomId: string, params: object) => {
    return httpRequestAuth().get(chat.FETCH_MESSAGE_LIST.replace(':id', roomId), {params})
}

export const archiveRoom = (roomId: string) => {
    return httpRequestAuth().put(chat.ARCHIVE_ROOM.replace(':id', roomId))
}

export const fetchRoomOrderDetails = (orderId: string) => {
    return httpRequestAuth().get(chat.GET_ROOM_ORDER_DETAILS.replace(':orderId', orderId))
}