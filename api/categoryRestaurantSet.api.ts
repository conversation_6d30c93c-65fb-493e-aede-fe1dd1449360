import { httpRequestAuth } from './httpClient';
import { categoryRestaurantSet } from '~/constants/apiPaths';

export const fetchRestaurantsByCategory = (id: number) => {
    return httpRequestAuth().get(
        categoryRestaurantSet.FETCH_RESTAURANT_SETS_BY_CATEGORY_LIST.replace(':categoryId', id.toString()),
    );
};

export const create = (params: object) => {
    return httpRequestAuth().post(categoryRestaurantSet.CREATE_CATEGORY_RESTAURANT_SET, params);
};

export const remove = (body: object) => {
    return httpRequestAuth().delete(categoryRestaurantSet.DELETE_CATEGORY_RESTAURANT_SET, {
        data: body,
    });
};
