import { httpRequestAuth } from "./httpClient";
import { restaurantNewsfeeds } from "~/constants/apiPaths";

export const fetchList = (params: Object) => {
  return httpRequestAuth().get(restaurantNewsfeeds.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    restaurantNewsfeeds.FETCH_DETAILS.replace(":id", id.toString())
  );
};

export const createNewsfeeds = (params: Object) => {
  return httpRequestAuth().post(restaurantNewsfeeds.CREATE, params);
};

export const updateNewsfeeds = (id: number, params: Object) => {
  return httpRequestAuth().put(
    restaurantNewsfeeds.UPDATE.replace(":id", id.toString()),
    params
  );
};

export const updateNewsfeedsOrdinals = (params: Object) => {
  return httpRequestAuth().put(restaurantNewsfeeds.UPDATE_ORDINAL, params);
};

export const deleteNewsfeeds = (id: number) => {
  return httpRequestAuth().delete(
    restaurantNewsfeeds.DELETE.replace(":id", id.toString())
  );
};

export const fetchConditions = () => {
  return httpRequestAuth().get(restaurantNewsfeeds.FETCH_CONDITIONS);
};
