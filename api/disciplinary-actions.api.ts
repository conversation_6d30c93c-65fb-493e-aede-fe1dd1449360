import { IDriverDisciplinaryAction, IHttpResponse, IPagination } from '~/types';
import { httpRequestAuth } from './httpClient';
import { DRIVER_DISCIPLINARY_ACTION } from '~/constants/apiPaths';

export const fetchList = (payload: any) => {
    return httpRequestAuth().post(DRIVER_DISCIPLINARY_ACTION.FETCH_LIST, payload);
};

export const fetchListByDriverId = (params: any) => {
    const driverId = params.driver_id;
    return httpRequestAuth().get(
        DRIVER_DISCIPLINARY_ACTION.FETCH_LIST_BY_DRIVER_ID.replace(':id', driverId.toString()),
        { params },
    );
};

export const fetchDisciplineMethods = (code: any) => {
    return httpRequestAuth().get(DRIVER_DISCIPLINARY_ACTION.FETCH_DISCIPLINARY_METHOD.replace(':code', code));
};

export const exportGetList = (payload: object) => {
    return httpRequestAuth().post<IHttpResponse<IPagination<IDriverDisciplinaryAction>>>(
        DRIVER_DISCIPLINARY_ACTION.EXPORT_GET_LIST,
        payload,
    );
};

export const fetchActionCategories = (payload: object = {}) => {
    return httpRequestAuth().post(DRIVER_DISCIPLINARY_ACTION.FETCH_ACTION_CATEGORIES, payload);
};
