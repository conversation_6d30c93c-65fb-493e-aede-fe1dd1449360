import { httpRequestAuth } from './httpClient';
import { orderCancellationType } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(orderCancellationType.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(orderCancellationType.FETCH_DETAILS.replace(':id', id.toString()));
};

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(orderCancellationType.UPDATE.replace(':id', id.toString()), params);
};
