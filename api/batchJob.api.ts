import { httpRequestAuth } from './httpClient';
import { batchJob } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(batchJob.FETCH_LIST, { params });
};

export const getCount = (params: object) => {
    return httpRequestAuth().get(batchJob.GET_COUNT, { params });
};
export const getDetail = (id: number) => {
    return httpRequestAuth().get(batchJob.GET_DETAILS.replace(':id', id.toString()));
};
export const create = (body: object) => {
    return httpRequestAuth().post(batchJob.CREATE_BATCH_JOB, body);
};
export const putQueue = (id: number) => {
    return httpRequestAuth().post(batchJob.PUT_QUEUE.replace(':id', id.toString()));
};
export const retry = (ids: string[]) => {
    return httpRequestAuth().post(batchJob.RETRY, ids);
};
export const pause = (id: number, data: { paused: number }) => {
    return httpRequestAuth().put(batchJob.PAUSE.replace(':id', id.toString()), data);
};
export const deleteBatchJob = (id: number) => {
    return httpRequestAuth().delete(batchJob.DELETE.replace(':id', id.toString()))
};