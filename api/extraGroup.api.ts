import { httpRequestAuth } from "./httpClient";
import { extraGroups } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(extraGroups.FETCH_EXTRA_GROUP, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(extraGroups.FETCH_EXTRA_GROUP_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(extraGroups.UPDATE_EXTRA_GROUP.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(extraGroups.CREATE_EXTRA_GROUP, params)
}

export const deleteExtraGroup = (id: number) => {
    return httpRequestAuth().delete(extraGroups.DELETE_EXTRA_GROUP.replace(':id', id.toString()))
}

export const addGroupItems = (id: number, params: object) => {
    return httpRequestAuth().post(extraGroups.ADD_NEW_EXTRA_GROUP_ITEMS.replace(':id', id.toString()), params)
}

export const deleteGroupItem = (groupId: number, itemId: number) => {
    return httpRequestAuth().delete(extraGroups.DELETE_NEW_EXTRA_GROUP_ITEM.replace(':groupId', groupId.toString()).replace(':itemId', itemId.toString()))
}