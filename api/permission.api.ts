import { httpRequestAuth } from "./httpClient";
import { permissions } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
  return httpRequestAuth().get(permissions.FETCH_LIST, { params });
};

export const fetchAll = () => {
  return httpRequestAuth().get(permissions.FETCH_ALL);
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    permissions.FETCH_DETAILS.replace(":id", id.toString())
  );
};

export const update = (id: number, params: object) => {
  return httpRequestAuth().put(
    permissions.UPDATE.replace(":id", id.toString()),
    params
  );
};

export const create = (params: object) => {
  return httpRequestAuth().post(permissions.CREATE, params);
};

export const remove = (id: number) => {
  return httpRequestAuth().delete(
    permissions.DELETE.replace(":id", id.toString())
  );
};
