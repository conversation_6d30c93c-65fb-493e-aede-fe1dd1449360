import { httpRequestAuth } from "./httpClient";
import { shipperRankings } from "~/constants/apiPaths";

export const fetchList = () => {
    return httpRequestAuth().get(shipperRankings.FETCH_LIST)
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(shipperRankings.FETCH_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(shipperRankings.UPDATE.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(shipperRankings.CREATE, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(shipperRankings.DELETE.replace(':id', id.toString()))
}