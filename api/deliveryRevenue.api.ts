import { httpRequestAuth } from './httpClient';
import { deliveryRevenue } from '~/constants/apiPaths';

export const fetchList = (id: number, params: object) => {
    return httpRequestAuth().get(deliveryRevenue.FETCH_LIST.replace(':id', id.toString()), { params });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(deliveryRevenue.FETCH_DETAILS.replace(':id', id.toString()));
};
