import { httpRequestAuth } from "./httpClient";
import { frames } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(frames.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(frames.FETCH_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(frames.UPDATE.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(frames.CREATE, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(frames.DELETE.replace(':id', id.toString()))
}