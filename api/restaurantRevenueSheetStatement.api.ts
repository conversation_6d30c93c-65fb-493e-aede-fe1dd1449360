import { RESTAURANT_REVENUE_SHEET_STATEMENT } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchList = (params: any) => {
    return httpRequestAuth().get(RESTAURANT_REVENUE_SHEET_STATEMENT.FETCH_LIST, {
        params,
    });
};

export const downloadFile = (id: number) => {
    return httpRequestAuth().get(RESTAURANT_REVENUE_SHEET_STATEMENT.DOWNLOAD_FILE.replace(':id', id.toString()));
};

export const deleteFile = (fileName: string) => {
    return httpRequestAuth().delete(RESTAURANT_REVENUE_SHEET_STATEMENT.DELETE_FILE.replace(':id', fileName));
};
