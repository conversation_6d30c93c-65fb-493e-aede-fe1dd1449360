import { restaurantAd } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchAdCategoryList = () => {
    return httpRequestAuth().get(restaurantAd.FETCH_ADS_CATEGORY);
};

export const fetchRestaurantAd = (restaurantId: number) => {
    return httpRequestAuth().get(restaurantAd.FETCH_ADS.replace(':restaurantId', restaurantId.toString()));
};

export const fetchVillRestaurantAd = (params: any) => {
    return httpRequestAuth().get(restaurantAd.FETCH_VILL_RESTAURANT_ADS, { params });
};

export const fetchRestaurantAdDetails = (id: number) => {
    return httpRequestAuth().get(restaurantAd.FETCH_ADS_DETAILS.replace(':id', id.toString()));
};

export const deleteRestaurantAd = (id: number) => {
    return httpRequestAuth().delete(restaurantAd.DELETE_ADS.replace(':id', id.toString()));
};

export const createRestaurantAd = (params: object) => {
    return httpRequestAuth().post(restaurantAd.CREATE_ADS, params);
};

export const updateRestaurantAd = (id: number, params: object) => {
    return httpRequestAuth().put(restaurantAd.UPDATE_ADS.replace(':id', id.toString()), params);
};

export const getRestaurantsOfNewsfeedAd = (newsfeedId: number) => {
    return httpRequestAuth().get(
        restaurantAd.GET_RESTAURANTS_OF_NEWSFEED_AD.replace(':newsfeedId', newsfeedId.toString()),
    );
};

export const updateAdNewsfeedPositon = (params: object) => {
    return httpRequestAuth().put(restaurantAd.UPDATA_NEWSFEED_AD_POSITION, params);
};

export const getAdSummary = (params: object) => {
    return httpRequestAuth().get(restaurantAd.GET_ADS_SUMMARY, { params });
};
