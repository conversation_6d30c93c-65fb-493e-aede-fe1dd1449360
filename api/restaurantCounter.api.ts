import { httpRequestAuth } from './httpClient';
import { restaurantCounter } from '~/constants/apiPaths';

export const fetchList = () => {
    return httpRequestAuth().get(restaurantCounter.FETCH_LIST);
};

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(restaurantCounter.UPDATE.replace(':id', id.toString()), params);
};

export const create = (params: object) => {
    return httpRequestAuth().post(restaurantCounter.CREATE, params);
};

export const generateCode = (subProvinceId: string) => {
    return httpRequestAuth().get(restaurantCounter.GENERATE_CODE, { params: { subProvinceId } });
};
