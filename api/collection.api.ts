import { httpRequestAuth } from "./httpClient";
import { collections } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(collections.FETCH_COLLECTION_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(collections.FETCH_COLLECTION_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(collections.UPDATE_COLLECTION.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(collections.CREATE_COLLECTION, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(collections.DELETE_COLLECTION.replace(':id', id.toString()))
}
