import { fraudDetectionReport } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';
export const fetchList = (params: any) => {
    return httpRequestAuth().get(fraudDetectionReport.FETCH_LIST, { params });
};
export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(fraudDetectionReport.FETCH_DETAIL.replace(':id', id.toString()));
};

export const update = (id: number, body: any) => {
    return httpRequestAuth().put(fraudDetectionReport.UPDATE.replace(':id', id.toString()), body);
};
