import { httpRequestAuth } from './httpClient';
import { deliveryAddresses } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(deliveryAddresses.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(deliveryAddresses.FETCH_DETAILS.replace(':id', id.toString()));
};

export const remove = (id: number) => {
    return httpRequestAuth().delete(deliveryAddresses.DELETE.replace(':id', id.toString()));
};

export const update = (params: object) => {
    return httpRequestAuth().post(deliveryAddresses.UPDATE, { params });
};
