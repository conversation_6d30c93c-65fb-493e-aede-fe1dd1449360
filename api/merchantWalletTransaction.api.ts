import { httpRequestAuth } from "./httpClient";
import { merchantWalletTransaction } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(merchantWalletTransaction.FETCH_BALANCE_TRANSACTION_LIST, {params})
}

export const fetchOrderTransactions = (params: object) => {
    return httpRequestAuth().get(merchantWalletTransaction.FETCH_ORDER_TRANSACTION_LIST, {params})
}

export const fetchChargeTransactions = (params: object) => {
    return httpRequestAuth().get(merchantWalletTransaction.FETCH_CHARGE_TRANSACTION_LIST, {params})
}

export const fetchRefundApplicationFees = (params: object) => {
    return httpRequestAuth().get(merchantWalletTransaction.FETCH_REFUND_APPLICATION_FEE_LIST, {params})
}

export const fetchPayoutTransactions = (params: object) => {
    return httpRequestAuth().get(merchantWalletTransaction.FETCH_PAYOUT_TRANSACTION_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(merchantWalletTransaction.FETCH_BALANCE_TRANSACTION_DETAILS.replace(':id', id.toString()))
}

export const fetchOrderTransactionDetails = (id: string) => {
    return httpRequestAuth().get(merchantWalletTransaction.FETCH_ORDER_TRANSACTION_DETAILS.replace(':id', id))
}

export const fetchChargeTransactionDetails = (id: string) => {
    return httpRequestAuth().get(merchantWalletTransaction.FETCH_CHARGE_TRANSACTION_DETAILS.replace(':id', id))
}

export const fetchPayoutTransactionDetails = (id: string) => {
    return httpRequestAuth().get(merchantWalletTransaction.FETCH_PAYOUT_TRANSACTION_DETAILS.replace(':id', id))
}

export const fetchRefundApplicationFeeDetails = (id: string) => {
    return httpRequestAuth().get(merchantWalletTransaction.FETCH_REFUND_APPLICATION_FEE_DETAILS.replace(':id', id))
}

export const exportOrderTransactions = (params: Record<string, any>) => {
    return httpRequestAuth().get(merchantWalletTransaction.EXPORT_ORDER_TRANSACTION_LIST, {params})
}

export const exportChargeTransactions = (params: Record<string, any>) => {
    return httpRequestAuth().get(merchantWalletTransaction.EXPORT_CHARGE_TRANSACTION_LIST, {params})
}

export const exportRefundApplicationFeeTransactions = (params: Record<string, any>) => {
    return httpRequestAuth().get(merchantWalletTransaction.EXPORT_REFUND_APPLICATION_FEE_LIST, {params})
}

export const exportBalanceTransactions = (params: Record<string, any>) => {
    return httpRequestAuth().get(merchantWalletTransaction.EXPORT_BALANCE_TRANSACTION_LIST, {params})
}

export const exportPaypoutTransactions = (params: Record<string, any>) => {
    return httpRequestAuth().get(merchantWalletTransaction.EXPORT_PAYOUT_TRANSACTION_LIST, {params})
}