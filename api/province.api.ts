import { httpRequest, httpRequestAuth } from './httpClient';
import { province } from '~/constants/apiPaths';
import { getProvinceIdStorage } from '~/utils/common';
import axios from 'axios';
import { getAppConfigs } from '~/configs';

export const fetchAllProvinces = () => {
    return httpRequest().get(province.FETCH_ALL_PROVINCES);
};

export const fetchActiveProvinces = () => {
    return httpRequest().get(province.FETCH_ALL_ACTIVE_PROVINCES);
};

export const fetchMainProvinces = () => {
    return httpRequest().get(province.FETCH_MAIN_PROVINCES);
};

export const fetchSubProvinces = () => {
    const provinceId = getProvinceIdStorage();
    return httpRequest().get(province.FETCH_SUB_PROVINCES.replace(':id', provinceId));
};

export const fetchSubProvincesByProvinceId = (provinceId: string) => {
    const { APP_DOMAIN: baseURL } = getAppConfigs();

    const http = axios.create({
        baseURL: baseURL,
        timeout: 120000,
        headers: {
            'X-Province': provinceId,
        },
    });
    return http.get(province.FETCH_SUB_PROVINCES.replace(':id', provinceId));
};

export const fetchProvinceDetails = () => {
    return httpRequest().get(province.FETCH_PROVINCE_DETAILS);
};
