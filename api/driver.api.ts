import { httpRequestAuth } from './httpClient';
import { drivers } from '~/constants/apiPaths';

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(drivers.GET_DRIVER.replace(':id', id.toString()));
};

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(drivers.UPDATE_DRIVER.replace(':id', id.toString()), params);
};

export const createOnepayCollectUser = (id: number) => {
    return httpRequestAuth().post(drivers.CREATE_ONEPAY_COLLECT_USER.replace(':id', id.toString()));
};

export const getOnePayCollectUser = (id: number) => {
    return httpRequestAuth().get(drivers.GET_ONEPAY_COLLECT_USER.replace(':id', id.toString()));
};

export const updateOnePayCollectUser = (id: number, params: Record<string, any>) => {
    return httpRequestAuth().put(drivers.UPDATE_ONEPAY_COLLECT_USER.replace(':id', id.toString()), params);
};

export const getBankAccounts = (id: number) => {
    return httpRequestAuth().get(drivers.GET_BANK_ACCOUNTS.replace(':id', id.toString()));
};

export const createBankAccount = (driverId: number, payload: Record<string, any>) => {
    return httpRequestAuth().post(drivers.CREATE_BANK_ACCOUNT.replace(':id', driverId.toString()), payload);
};

export const updateBankAccount = (driverId: number, bankAccountId: number, params: Record<string, any>) => {
    return httpRequestAuth().put(
        drivers.UPDATE_BANK_ACCOUNT.replace(':id', driverId.toString()).replace(
            ':bankAccountId',
            bankAccountId.toString(),
        ),
        params,
    );
};

export const createIdCard = (driverId: number, payload: Record<string, any>) => {
    return httpRequestAuth().post(drivers.CREATE_ID_CARD.replace(':id', driverId.toString()), payload);
};

export const updateIdCard = (driverId: number, params: Record<string, any>) => {
    return httpRequestAuth().put(drivers.UPDATE_ID_CARD.replace(':id', driverId.toString()), params);
};

export const getIdCard = (driverId: number) => {
    return httpRequestAuth().get(drivers.GET_ID_CARD.replace(':id', driverId.toString()));
};

export const getDriverJobSetting = (driverId: number) => {
    return httpRequestAuth().get(drivers.GET_JOB_SETTING.replace(':id', driverId.toString()));
};
export const updateDriverJobSetting = (driverId: number, params: Object) => {
    return httpRequestAuth().put(drivers.UPDATE_JOB_SETTING.replace(':id', driverId.toString()), params);
};
export const createDriverJobSetting = (driverId: number) => {
    return httpRequestAuth().post(drivers.CREATE_JOB_SETTING.replace(':id', driverId.toString()));
};

export const getDriverJobStats = (driverId: number) => {
    return httpRequestAuth().get(drivers.GET_JOB_STATS.replace(':id', driverId.toString()));
};

export const updateDriverJobStats = (driverId: number, params: Object) => {
    return httpRequestAuth().put(drivers.UPDATE_JOB_STATS.replace(':id', driverId.toString()), params);
};

export const createDriverJobStats = (driverId: number) => {
    return httpRequestAuth().post(drivers.CREATE_JOB_STATS.replace(':id', driverId.toString()));
};

export const addShiftWork = (driverId: number, params: Record<string, any>) => {
    return httpRequestAuth().post(drivers.ADD_SHIFT_WORK.replace(':id', driverId.toString()), params);
};

export const removeShiftWork = (driverId: number, params: Record<string, any>) => {
    return httpRequestAuth().delete(drivers.REMOVE_SHIFT_WORK.replace(':id', driverId.toString()), { data: params });
};

export const fetchShiftWorks = (driverId: number) => {
    return httpRequestAuth().get(drivers.FETCH_SHIFT_WORKS.replace(':id', driverId.toString()));
};

export const fetchDriverDevices = (driverId: number) => {
    return httpRequestAuth().get(drivers.FETCH_DRIVER_DEVICES.replace(':driverId', driverId.toString()));
};

export const deleteDriverDevice = (id: number, driverId: number) => {
    return httpRequestAuth().delete(
        drivers.DELETE_DRIVER_DEVICE.replace(':deviceId', id.toString()).replace(':driverId', driverId.toString()),
    );
};
export const updateDeviceAuthStatus = (id: number, driverId: number, params: Object) => {
    return httpRequestAuth().put(
        drivers.LOGOUT_DRIVER_DEVICE.replace(':deviceId', id.toString()).replace(':driverId', driverId.toString()),
        params,
    );
};

export const fetchDriverAuthHistories = (driverId: number, params: Record<string, any>) => {
    return httpRequestAuth().get(drivers.FETCH_DRIVER_AUTH_HISTORY.replace(':driverId', driverId.toString()), {
        params: params,
    });
};

export const fetchDriverBankAccountList = (params: Record<string, any>) => {
    return httpRequestAuth().get(drivers.FETCH_DRIVER_BANK_ACCOUNT_LIST, {
        params: params,
    });
};

export const fetchDriverInfoList = (params: Record<string, any>) => {
    return httpRequestAuth().get(drivers.FETCH_DRIVER_INFO_LIST, {
        params: params,
    });
};

export const createEpayAccount = (driverId: number, params: Record<string, any>) => {
    return httpRequestAuth().post(drivers.CREATE_EPAY_ACCOUNT.replace(':id', driverId.toString()), params);
};

export const getEpayCollectUser = (driverId: number) => {
    return httpRequestAuth().get(drivers.GET_EPAY_COLLECT_USER.replace(':id', driverId.toString()));
};

export const updateEpayCollectUser = (driverId: number) => {
    return httpRequestAuth().put(drivers.UPDATE_EPAY_COLLECT_USER.replace(':id', driverId.toString()));
};

export const cancelEpayCollectUser = (driverId: number) => {
    return httpRequestAuth().delete(drivers.CANCEL_EPAY_COLLECT_USER.replace(':id', driverId.toString()));
};

export const fetchDriverFaceRecognition = (driverId: number, params: any) => {
    return httpRequestAuth().get(drivers.FETCH_FACE_RECOGNITIONS.replace(':driverId', driverId.toString()), {
        params,
    });
};

export const fetchDriverFaceRecognitionFilter = (params: any) => {
    return httpRequestAuth().get(drivers.FETCH_FACE_RECOGNITIONS_FILTER, {
        params,
    });
};

export const updateDriverFaceRecognition = (id: number, driverId: number, body: Object) => {
    return httpRequestAuth().put(
        drivers.UPDATE_FACE_RECOGNITIONS.replace(':id', id.toString()).replace(':driverId', driverId.toString()),
        body,
    );
};

export const createDriverFaceRecognition = (driverId: number, body: Object) => {
    return httpRequestAuth().post(drivers.CREATE_FACE_RECOGNITIONS.replace(':driverId', driverId.toString()), body);
};

export const deleteDriverFaceRecognition = (id: number, driverId: number) => {
    return httpRequestAuth().delete(
        drivers.DELETE_FACE_RECOGNITIONS.replace(':id', id.toString()).replace(':driverId', driverId.toString()),
    );
};

export const fetchDriverVehicle = (driverId: number) => {
    return httpRequestAuth().get(drivers.FETCH_DRIVER_VEHICLE.replace(':driverId', driverId.toString()));
};

export const createDriverVehicle = (driverId: number, body: Object) => {
    return httpRequestAuth().post(drivers.CREATE_DRIVER_VEHICLE.replace(':driverId', driverId.toString()), body);
};

export const updateDriverVehicle = (id: number, driverId: number, body: Object) => {
    return httpRequestAuth().put(
        drivers.UPDATE_DRIVER_VEHICLE.replace(':id', id.toString()).replace(':driverId', driverId.toString()),
        body,
    );
};

export const deleteDriverVehicle = (id: number, driverId: number) => {
    return httpRequestAuth().delete(
        drivers.DELETE_DRIVER_VEHICLE.replace(':id', id.toString()).replace(':driverId', driverId.toString()),
    );
};

//SECTION -  9pay

export const create9PayAccount = (driverId: number, params: Record<string, any>) => {
    return httpRequestAuth().post(drivers.CREATE_9PAY_ACCOUNT.replace(':id', driverId.toString()), params);
};

export const get9PayCollectUser = (driverId: number) => {
    return httpRequestAuth().get(drivers.GET_9PAY_COLLECT_USER.replace(':id', driverId.toString()));
};

export const generateCode = (params: any) => {
    return httpRequestAuth().get(drivers.GENERATE_CODE, {
        params,
    });
};

export const getTaxInformation = (driverId: number) => {
    return httpRequestAuth().get(drivers.GET_TAX_INFORMATION.replace(':id', driverId.toString()));
};

export const createTaxInformation = (driverId: number, body: Object) => {
    return httpRequestAuth().post(drivers.CREATE_TAX_INFORMATION.replace(':id', driverId.toString()), body);
};

export const updateTaxInformation = (driverId: number, body: Object) => {
    return httpRequestAuth().put(drivers.UPDATE_TAX_INFORMATION.replace(':id', driverId.toString()), body);
};
