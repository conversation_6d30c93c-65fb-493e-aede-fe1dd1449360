import { httpRequestAuth } from './httpClient';
import { restaurantSheetStatement } from '~/constants/apiPaths';

export const fetchList = (params: any) => {
    return httpRequestAuth().get(restaurantSheetStatement.FETCH_LIST, {
        params,
    });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(restaurantSheetStatement.FETCH_DETAILS.replace(':id', id.toString()));
};

export const downloadFile = (id: number) => {
    return httpRequestAuth().get(restaurantSheetStatement.DOWNLOAD_FILE.replace(':id', id.toString()));
};

export const ListFile = (params: object) => {
    return httpRequestAuth().get(restaurantSheetStatement.FETCH_FILES, { params });
};

export const deleteFile = (fileName: string) => {
    return httpRequestAuth().delete(restaurantSheetStatement.DELETE_FILE.replace(':fileName', fileName));
};

export const deleteSheet = (id: number) => {
    return httpRequestAuth().delete(restaurantSheetStatement.DELETE_SHEET.replace(':id', id.toString()));
};
