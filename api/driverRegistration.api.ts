import { httpRequestAuth } from './httpClient';
import { driverRegistration, drivers } from '~/constants/apiPaths';

export const fetchList = (params: Object) => {
    return httpRequestAuth().get(driverRegistration.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(driverRegistration.FETCH_DETAILS.replace(':id', id.toString()));
};

export const update = (id: number, data: Object) => {
    return httpRequestAuth().put(driverRegistration.UPDATE.replace(':id', id.toString()), data);
};
