import { httpRequestAuth } from './httpClient';
import { DRIVER_AGREEMENT_FOR_ADMIN } from '~/constants/apiPaths';

export const fetchList = (params: unknown) => {
  return httpRequestAuth().get(DRIVER_AGREEMENT_FOR_ADMIN.FETCH_LIST, { params });
};

export const update = (id: number) => {
  return httpRequestAuth().patch(DRIVER_AGREEMENT_FOR_ADMIN.UPDATE.replace(':id', id.toString()));
};

export const fetchDetail = (id: number) => {
  return httpRequestAuth().get(DRIVER_AGREEMENT_FOR_ADMIN.FETCH_DETAILS.replace(':id', id.toString()));
};

export const approve = (id: number) => {
  return httpRequestAuth().post(DRIVER_AGREEMENT_FOR_ADMIN.APPROVE.replace(':id', id.toString()));
};

export const reject = (id: number, reason: string) => {
  return httpRequestAuth().post(DRIVER_AGREEMENT_FOR_ADMIN.REJECT.replace(':id', id.toString()), { reason });
};

export const remove = (id: number) => {
  return httpRequestAuth().delete(DRIVER_AGREEMENT_FOR_ADMIN.REMOVE.replace(':id', id.toString()));
};

export const reGenerate = (id: number) => {
  return httpRequestAuth().post(DRIVER_AGREEMENT_FOR_ADMIN.RE_GENERATE.replace(':id', id.toString()));
};

export const getStatistics = (id: number) => {
  return httpRequestAuth().get(DRIVER_AGREEMENT_FOR_ADMIN.GET_STATISTICS.replace(':id', id.toString()));
};

export const reGenerateForNewDriver = (id: number) => {
  return httpRequestAuth().post(DRIVER_AGREEMENT_FOR_ADMIN.RE_GENERATE_NEW_DRIVER.replace(':id', id.toString()));
}