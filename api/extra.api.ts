import { httpRequestAuth } from "./httpClient";
import { extras } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(extras.FETCH_EXTRA_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(extras.FETCH_EXTRA_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(extras.UPDATE_EXTRA.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(extras.CREATE_EXTRA, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(extras.DELETE_EXTRA.replace(':id', id.toString()))
}

