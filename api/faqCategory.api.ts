import { httpRequestAuth } from "./httpClient";
import { faqCategories } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(faqCategories.FETCH_LIST, {params})
}

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(faqCategories.FETCH_DETAILS.replace(':id', id.toString()))
}

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(faqCategories.UPDATE.replace(':id', id.toString()), params)
}

export const create = (params: object) => {
    return httpRequestAuth().post(faqCategories.CREATE, params)
}

export const remove = (id: number) => {
    return httpRequestAuth().delete(faqCategories.DELETE.replace(':id', id.toString()))
}