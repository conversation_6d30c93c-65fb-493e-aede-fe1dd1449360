import { httpRequestAuth } from './httpClient';
import { SIGNATURES } from '~/constants/apiPaths';

export const fetchList = () => {
  return httpRequestAuth().get(SIGNATURES.FETCH_LIST);
};

export const deleteSignature = (id: number) => {
  return httpRequestAuth().delete(SIGNATURES.DELETE.replace(':id', id.toString()));
};

export const createSignature = (data: any) => {
  return httpRequestAuth().post(SIGNATURES.CREATE, data);
};

export const getPresigned = () => {
  return httpRequestAuth().get(SIGNATURES.GET_PRESIGNED);
};
