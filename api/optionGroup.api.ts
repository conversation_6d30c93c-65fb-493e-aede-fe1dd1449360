import { httpRequestAuth } from './httpClient';
import { optionGroups } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(optionGroups.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(optionGroups.FETCH_DETAILS.replace(':id', id.toString()));
};

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(optionGroups.UPDATE.replace(':id', id.toString()), params);
};

export const create = (params: object) => {
    return httpRequestAuth().post(optionGroups.CREATE, params);
};

export const remove = (id: number) => {
    return httpRequestAuth().delete(optionGroups.DELETE.replace(':id', id.toString()));
};

export const createBulk = (params: object) => {
    return httpRequestAuth().post(optionGroups.CREATE_BULK, params);
};
