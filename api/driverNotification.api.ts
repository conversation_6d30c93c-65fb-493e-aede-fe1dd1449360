import { httpRequestAuth } from './httpClient';
import { driverNotification } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(driverNotification.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(driverNotification.FETCH_DETAILS.replace(':id', id.toString()));
};

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(driverNotification.UPDATE.replace(':id', id.toString()), params);
};

export const create = (params: object) => {
    return httpRequestAuth().post(driverNotification.CREATE, params);
};

export const remove = (id: number) => {
    return httpRequestAuth().delete(driverNotification.REMOVE.replace(':id', id.toString()));
};
