import { httpRequestAuth } from './httpClient';
import { SUB_SETTINGS } from '~/constants/apiPaths';

export const getSubSettings = (subProvinceId: number) => {
  return httpRequestAuth().get(SUB_SETTINGS.FETCH_SUB_SETTINGS, {
    params: { subProvinceId },
  });
};

export const updateSubSettings = (body: Record<string, any>) => {
  return httpRequestAuth().put(SUB_SETTINGS.UPDATE_SUB_SETTINGS_BY_KEY, body);
};

export const getSubSettingsByKey = (subProvinceId: number, key: string) => {
  return httpRequestAuth().get(SUB_SETTINGS.FETCH_SUB_SETTING_BY_KEY, {
    params: { subProvinceId, key },
  });
};

export const updateSubSettingDefaultTax = (params: object) => {
    return httpRequestAuth().put(SUB_SETTINGS.UPDATE_SUB_SETTINGS_DEFAULT_TAX, params);
  };
