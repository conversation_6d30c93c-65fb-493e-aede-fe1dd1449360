import { feeds } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchFeedList = (params: object) => {
    return httpRequestAuth().get(feeds.FETCH_LIST, {
        params: params,
    });
};

export const create = (params: object) => {
    return httpRequestAuth().post(feeds.CREATE_FEED, params);
};

export const fetchSourceList = (params: object) => {
    return httpRequestAuth().get(feeds.FETCH_SOURCE_LIST, {
        params: params,
    });
};

export const updateFeed = (id: number, params: object) => {
    return httpRequestAuth().put(feeds.UPDATE_FEED + id, params);
};

export const updateMultipleStatus = (params: object) => {
    return httpRequestAuth().post(feeds.UPDATE_STATUS_FEED, params);
};

export const deleteFeed = (id: number) => {
    return httpRequestAuth().delete(feeds.DELETE_FEED + id);
};

export const getFeed = (id: number) => {
    return httpRequestAuth().get(feeds.GET_FEED + id);
};

export const getHashTagList = (params: object) => {
    return httpRequestAuth().get(feeds.FETCH_HASHTAG_LIST, {
        params: params,
    });
};

export const getPostReportList = (id: number, params: object) => {
    return httpRequestAuth().get(feeds.FETCH_POST_REPORT_LIST + id, params);
};

export const generateDynamicLink = (id: number) => {
    return httpRequestAuth().post(feeds.GENERATE_DYNAMIC_LINK.replace(':postId', id.toString()));
};

export const updateMedia = (params: object) => {
    return httpRequestAuth().put(feeds.UPDATE_MEDIA, params);
};

export const deleteMedia = (id: number) => {
    return httpRequestAuth().delete(feeds.DELETE_MEDIA + id);
};
