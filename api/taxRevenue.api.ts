import { httpRequestAuth } from './httpClient';
import { TAX_REVENUE } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(TAX_REVENUE.FETCH_LIST, { params });
};

export const fetchDetail = (id: number) => {
    return httpRequestAuth().get(TAX_REVENUE.FETCH_DETAILS.replace(':id', id.toString()));
};

export const deleteTaxRevenue = (id: number) => {
    return httpRequestAuth().delete(TAX_REVENUE.DELETE.replace(':id', id.toString()));
};

export const fetchTaxOrderList = (id: number, params: object) => {
    return httpRequestAuth().get(TAX_REVENUE.FETCH_TAX_ORDER_LIST.replace(':id', id.toString()), { params });
};

export const downloadFile = (id: number, params: object) => {
    return httpRequestAuth().post(TAX_REVENUE.DOWNLOAD_FILE.replace(':id', id.toString()), params);
};

export const sendRevenueMail = (id: number, params: object) => {
    return httpRequestAuth().post(TAX_REVENUE.SEND_REVENUE_MAIL.replace(':id', id.toString()), params);
};

export const getAggRestaurantList = (id: number, params: object) => {
    return httpRequestAuth().get(TAX_REVENUE.FETCH_AGG_RESTAURANT_LIST.replace(':id', id.toString()), { params });
};

export const getAggDriverList = (id: number, params: object) => {
    return httpRequestAuth().get(TAX_REVENUE.FETCH_AGG_DRIVER_LIST.replace(':id', id.toString()), { params });
};

export const getStats = (id: number) => {
    return httpRequestAuth().get(TAX_REVENUE.FETCH_STATS.replace(':id', id.toString()));
};

export const getRestaurantOrderSummary = (id: number, restaurantId: number) => {
    return httpRequestAuth().get(
        TAX_REVENUE.FETCH_TAX_ORDER_SUMMARY.replace(':id', id.toString()).replace(
            ':restaurantId',
            restaurantId.toString(),
        ),
    );
};
