import { orderRejectionLog } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchListByDriverId = (userId: number, params: object) => {
    return httpRequestAuth().get(orderRejectionLog.FETCH_LIST_BY_DRIVER_ID.replace(':driverId', userId.toString()), {
        params,
    });
};

export const fetchListByOrderId = (orderId: number) => {
    return httpRequestAuth().get(orderRejectionLog.FETCH_LIST_BY_ORDER_ID.replace(':orderId', orderId.toString()));
};
