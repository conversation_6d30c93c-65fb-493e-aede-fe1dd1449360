import { httpRequestAuth } from './httpClient';
import { foodCategories } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
    return httpRequestAuth().get(foodCategories.FETCH_LIST, { params });
};

export const fetchAllList = (params: object) => {
    return httpRequestAuth().get(foodCategories.FETCH_ALL_LIST, { params });
};

export const fetchDetails = (id: number) => {
    return httpRequestAuth().get(foodCategories.FETCH_DETAILS.replace(':id', id.toString()));
};

export const update = (id: number, params: object) => {
    return httpRequestAuth().put(foodCategories.UPDATE.replace(':id', id.toString()), params);
};

export const create = (params: object) => {
    return httpRequestAuth().post(foodCategories.CREATE, params);
};

export const remove = (id: number) => {
    return httpRequestAuth().delete(foodCategories.DELETE.replace(':id', id.toString()));
};
export const updateOrdinalNumbers = (id: number, params: any) => {
    return httpRequestAuth().put(foodCategories.UPDATE_ORDINAL_NUMBERS.replace(':id', id.toString()), params);
};
