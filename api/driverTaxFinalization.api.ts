import { DRIVER_TAX_FINALIZATION } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchDriverList = (id: number, params: object) => {
    return httpRequestAuth().post(DRIVER_TAX_FINALIZATION.GET_LIST, {
        driver_id: id,
        ...params,
    });
};

export const fetchList = (params: object) => {
    return httpRequestAuth().post(DRIVER_TAX_FINALIZATION.GET_LIST, params);
};

export const create = (body: Record<string, any>) => {
    return httpRequestAuth().post(DRIVER_TAX_FINALIZATION.CREATE, body);
};

export const bulkCreate = (body: Record<string, any>) => {
    return httpRequestAuth().post(DRIVER_TAX_FINALIZATION.BULK_CREATE, body);
};

export const createEvent = (id: number, body: Record<string, any>) => {
    return httpRequestAuth().post(DRIVER_TAX_FINALIZATION.CREATE_EVENT.replace(':id', id.toString()), body);
};

export const fetchListWithStats = (body: Record<string, any>) => {
    return httpRequestAuth().post(DRIVER_TAX_FINALIZATION.GET_LIST_WITH_STATS, body);
};
