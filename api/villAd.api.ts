import { villAds } from "~/constants/apiPaths";
import { httpRequestAuth } from "./httpClient";

export const fetchVillAdsList = (params: object) => {
  return httpRequestAuth().get(villAds.FETCH_LIST, { params });
};

export const createVillAds = (params: object) => {
  return httpRequestAuth().post(villAds.CREATE, params);
};

export const updateVillAds = (id: number, params: object) => {
  return httpRequestAuth().put(
    villAds.UPDATE.replace(":id", id.toString()),
    params
  );
};

export const updateVillAdsRestaurant = (id: number, params: object) => {
  return httpRequestAuth().put(
    villAds.UPDATE_VILLAD_RESTAURANT.replace(":id", id.toString()),
    params
  );
};

export const fetchRestaurantAdsDetails = (id: number) => {
  return httpRequestAuth().get(
    villAds.FETCH_RESTAURANT_ADS_DETAILS.replace(":id", id.toString())
  );
};
