import { httpRequestAuth } from './httpClient';
import { AdBannerMerchants } from '~/constants/apiPaths';

export const fetchList = (params: object) => {
  return httpRequestAuth().get(AdBannerMerchants.FETCH_LIST, { params });
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(AdBannerMerchants.FETCH_DETAILS.replace(':id', id.toString()));
};

export const update = (id: number, params: object) => {
  return httpRequestAuth().put(AdBannerMerchants.UPDATE.replace(':id', id.toString()), params);
};

export const create = (params: object) => {
  return httpRequestAuth().post(AdBannerMerchants.CREATE, params);
};

export const remove = (id: number) => {
  return httpRequestAuth().delete(AdBannerMerchants.DELETE.replace(':id', id.toString()));
};
