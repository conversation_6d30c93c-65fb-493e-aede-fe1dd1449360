import { httpRequestAuth } from "./httpClient";
import { dashboard } from "~/constants/apiPaths";

export const fetchSummary = () => {
    return httpRequestAuth().get(dashboard.SUMMARY)
}

export const fetchClientIncomeSummary = (params: object) => {
    return httpRequestAuth().get(dashboard.CLIENT_INCOME_DATA, {params})
}

export const fetchRestaurantIncomeSummary = (params: object) => {
    return httpRequestAuth().get(dashboard.RESTAURANT_INCOME_DATA, {params})
}
