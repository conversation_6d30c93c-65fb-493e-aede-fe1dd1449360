import { DRIVER_REWARD } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchListByDriverId = (params: object) => {
    return httpRequestAuth().post(DRIVER_REWARD.FETCH_DRIVER_PAGED_LIST_BY_DRIVER_ID, params);
};

export const create = (body: Record<string, any>) => {
    return httpRequestAuth().post(DRIVER_REWARD.CREATE_REWARD, body);
};

export const fetchRewardTypes = () => {
    return httpRequestAuth().get(DRIVER_REWARD.FETCH_REWARD_TYPES);
}

export const sendTransactionRequest = (payload: any) => {
    return httpRequestAuth().post(DRIVER_REWARD.SEND_TRANSACTION_REQUEST, payload);
}

export const exportGetList = (params: object) => {
    return httpRequestAuth().post(DRIVER_REWARD.EXPORT_GET_LIST, params);
}