import { httpRequestAuth } from "./httpClient";
import { NPAY_WALLET } from "~/constants/apiPaths";

/**
 * <PERSON>ên kết ví 9Pay với tài khoản tài xế
 * @param params - Thông tin liên kết (driver_id, phone)
 * @returns Promise với phản hồi API
 */
export const linkWallet = (params: { driver_id: number; phone: string }) => {
    return httpRequestAuth().post(NPAY_WALLET.LINK, params);
};

/**
 * Lấy số dư ví 9Pay đã liên kết
 * @param params - Tham số truy vấn (nếu cần)
 * @returns Promise với phản hồi API chứa thông tin số dư
 */
export const getWalletBalance = (params?: object) => {
    return httpRequestAuth().post(NPAY_WALLET.GET_BALANCE, params || {});
};

/**
 * Đ<PERSON>g băng một số tiền cụ thể trong ví 9Pay đã liên kết (mặc định là 500,000)
 * @param params - Tham số truy vấn (nếu cần)
 * @returns Promise với phản hồi API chứa kết quả đóng băng
 */
export const freezeWalletBalance = (params?: object) => {
    return httpRequestAuth().post(NPAY_WALLET.FREEZE, params || {});
};

/**
 * Giải phóng số tiền đã đóng băng trước đó trong ví 9Pay đã liên kết
 * @param params - Tham số truy vấn (nếu cần)
 * @returns Promise với phản hồi API chứa kết quả giải phóng
 */
export const defrostWalletBalance = (params?: object) => {
    return httpRequestAuth().post(NPAY_WALLET.DEFROST, params || {});
};

export const syncAccountLinkingStatus = (params?: object) => {
    return httpRequestAuth().post(NPAY_WALLET.SYNC_STATUS, params || {});
};