import { IDisciplinaryActionType, IDriverDisciplinaryAction, IHttpResponse } from '~/types';
import { driverDisciplines } from "../constants/apiPaths";
import { httpRequestAuth } from "./httpClient";

export const fetchList = () => {
  return httpRequestAuth().get(driverDisciplines.FETCH_LIST);
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    driverDisciplines.FETCH_DETAILS.replace(":id", id.toString())
  );
};

export const update = (id: number, params: any) => {
  return httpRequestAuth().put(
    driverDisciplines.UPDATE.replace(":id", id.toString()),
    params
  );
};

export const create = (params: any) => {
  return httpRequestAuth().post(driverDisciplines.CREATE, params);
};

export const remove = (id: number) => {
  return httpRequestAuth().delete(
    driverDisciplines.DELETE.replace(":id", id.toString())
  );
};

export const fetchActionList = (params: any) => {
  return httpRequestAuth().get<IHttpResponse<IDisciplinaryActionType[]>>(driverDisciplines.FETCH_ACTION_LIST, {params});
};

export const updateAction = (id: number, params: any) => {
  return httpRequestAuth().put(
    driverDisciplines.UPDATE_ACTION.replace(":id", id.toString()),
    params
  );
};

export const fetchActionDetails = (id: number) => {
  return httpRequestAuth().get(
    driverDisciplines.FETCH_ACTION_DETAILS.replace(":id", id.toString())
  );
};

export const createDriverDiscipline = (params: any) => {
  return httpRequestAuth().post(driverDisciplines.CREATE_DRIVER_ACTION, params);
}

export const retryDriverDisciplinaryAction = (id: number) => {
  return httpRequestAuth().put(driverDisciplines.RETRY_DRIVER_ACTION + id);
}
