import { httpRequestAuth } from "./httpClient";
import { restaurantUpdateHistory } from "~/constants/apiPaths";

export const fetchList = (params: object) => {
  return httpRequestAuth().get(restaurantUpdateHistory.FETCH_LIST, {
    params
  });
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    restaurantUpdateHistory.FETCH_DETAILS.replace(":id", id.toString())
  );
};
