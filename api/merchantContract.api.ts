import { merchantContract } from '~/constants/apiPaths';
import { httpRequestAuth } from './httpClient';

export const fetchList = (query: Record<string, string>) => {
    return httpRequestAuth().get(merchantContract.FETCH_LIST, {
        params: query
    });
}

export const getContract = (id: number) => {
    return httpRequestAuth().get(merchantContract.GET_MERCHANT_CONTRACT.replace(':id', id.toString()));
}

export const createMerchantContract = (body: Record<string, any>) => {
    return httpRequestAuth().post(merchantContract.CREATE_MERCHANT_CONTRACT, body);
};

export const createOneContract = (body: Record<string, any>) => {
    return httpRequestAuth().post(merchantContract.CREATE_ONE_CONTRACT, body);
};

export const createMultipleContract = (id: number, body: Record<string, any>) => {
    return httpRequestAuth().post(merchantContract.CREATE_MULTIPLE_CONTRACT.replace(':typeId', id.toString()), body);
};

export const createMultipleContractForRestaurant = (id: number, body: Record<string, any>) => {
    return httpRequestAuth().post(merchantContract.CREATE_MULTIPLE_CONTRACT_FOR_RESTAURANT.replace(':typeId', id.toString()), body);
};

export const updateMerchantContract = (id: number, body: Record<string, any>) => {
    return httpRequestAuth().put(merchantContract.UPDATE_CONTRACT.replace(':id', id.toString()), body);
};

export const deleteMerchantContract = (id: number) => {
    return httpRequestAuth().delete(merchantContract.DELETE_CONTRACT.replace(':id', id.toString()));
};

export const fetchContractTypes = (query: Record<string, string>) => {
    return httpRequestAuth().get(merchantContract.FETCH_CONTRACT_TYPES, {
        params: query
    });
}

export const getContractType = (id: number) => {
    return httpRequestAuth().get(merchantContract.FETCH_CONTRACT_TYPE.replace(':id', id.toString()));
}

export const getContractTypeDetails = (id: number) => {
    return httpRequestAuth().get(merchantContract.GET_CONTRACT_TYPE_DETAILS.replace(':id', id.toString()));
}

export const createContractType = (body: Record<string, any>) => {
    return httpRequestAuth().post(merchantContract.CREATE_CONTRACT_TYPE, body);
};

export const updateContractType = (id: number, body: Record<string, any>) => {
    return httpRequestAuth().put(merchantContract.UPDATE_CONTRACT_TYPE.replace(':id', id.toString()), body);
};

export const getPresignedUrl = () => {
    return httpRequestAuth().get(merchantContract.GET_PRESIGNED_URL);
}

export const getRestaurants = (query: Record<string, string>) => {
    return httpRequestAuth().get(merchantContract.GET_RESTAURANT_OF_CONTRACT, {
        params: query
    });
}

export const getRestaurantsNotYetContracts = (query: Record<string, string>) => {
    return httpRequestAuth().get(merchantContract.GET_RESTAURANTS_NOT_YET_CONTRACT, {
        params: query
    });
}

export const getRestaurantsHasContracts = (query: Record<string, string>) => {
    return httpRequestAuth().get(merchantContract.GET_RESTAURANTS_HAS_CONTRACT, {
        params: query
    });
}

export const getMerchants = (id: number, query: Record<string, string>) => {
    return httpRequestAuth().get(merchantContract.GET_MERCHANTS.replace(':id', id.toString()), {
        params: query
    });
}

export const exportMerchantContract = (params: object) => {
    return httpRequestAuth().get(merchantContract.EXPORT_MERCHANT_CONTRACT, { params });
};
