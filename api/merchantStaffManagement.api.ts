import { httpRequestAuth } from './httpClient';
import { merchantStaffManagement } from '~/constants/apiPaths';
export const fetchRoleList = () => {
    return httpRequestAuth().get(merchantStaffManagement.FETCH_MERCHANT_ROLE_LIST);
};

export const fetchMerchantRoleDetails = (roleId: number) => {
    return httpRequestAuth().get(merchantStaffManagement.FETCH_MERCHANT_ROLE_DETAILS.replace(':id', roleId.toString()));
};

export const fetchMerchantPermissionList = () => {
    return httpRequestAuth().get(merchantStaffManagement.FETCH_MERCHANT_PERMISSION_LIST);
};

export const updateMerchantRoleDetails = (roleId: number, data: any) => {
    return httpRequestAuth().put(
        merchantStaffManagement.UPDATE_MERCHANT_ROLE_DETAILS.replace(':id', roleId.toString()),
        data,
    );
};
