import { driverReportTypes } from "~/constants/apiPaths";
import { httpRequestAuth } from "./httpClient";

export const fetchList = () => {
  return httpRequestAuth().get(driverReportTypes.FETCH_LIST);
};

export const fetchDetails = (id: number) => {
  return httpRequestAuth().get(
    driverReportTypes.FETCH_DETAILS.replace(":id", id.toString())
  );
};

export const create = (data: any) => {
  return httpRequestAuth().post(driverReportTypes.CREATE, data);
};

export const update = (id: number, data: any) => {
  return httpRequestAuth().put(
    driverReportTypes.UPDATE.replace(":id", id.toString()),
    data
  );
};

export const deleteType = (id: number) => {
  return httpRequestAuth().delete(
    driverReportTypes.DELETE.replace(":id", id.toString())
  );
};
