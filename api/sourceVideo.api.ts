import { sourceVideo } from "~/constants/apiPaths";
import { httpRequestAuth } from "./httpClient";

export const fetchList = (params: object) => {
    return httpRequestAuth().get(sourceVideo.FETCH_LIST, {
      params: params
    });
};

export const getPresignedUrlForSocial = (params: Record<string, string>) => {
  return httpRequestAuth().post(sourceVideo.GET_PRESIGNED_URL_FOR_SOCIAL, params)
}

export const createMediaForSocial = (params: any) => {
  return httpRequestAuth().post(sourceVideo.UPLOAD_SOCIAL_FILE, params)
}

export const uploadSocialFile = (params: any) => {
  return httpRequestAuth().post(sourceVideo.UPLOAD_SOCIAL_FILE, params)
}

export const getCoverUrlForSocial = (params: Record<string, string>) => {
  return httpRequestAuth().post(sourceVideo.GET_COVER_URL_FOR_SOCIAL, params)
}
