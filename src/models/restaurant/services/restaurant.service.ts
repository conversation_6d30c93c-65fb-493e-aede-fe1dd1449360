import { BadRequestException, HttpException, HttpStatus, Injectable, NotFoundException } from '@nestjs/common';
import * as _ from 'lodash';
import * as moment from 'moment';
import { In } from 'typeorm';
import { lastValueFrom } from 'rxjs';

import { createImageUrlDependingOnProvince } from 'src/common/helpers/common.helper';
import { isOpenByBusinessHours } from 'src/common/helpers/workTime.helper';
import { AppSetting, EAppSettingKey, IRestaurantCodeConfig } from 'src/entities/appSetting.entity';
import { Category } from 'src/entities/category.entity';
import { CategoryRestaurant } from 'src/entities/categoryRestaurant.entity';
import { Frame } from 'src/entities/frame.entity';
import { Merchant } from 'src/entities/merchant.entity';
import { MerchantHasRole } from 'src/entities/merchantHasRole.entity';
import { ERestaurantApprovalStatus, ERestaurantOperatingStatus, Restaurant } from 'src/entities/restaurant.entity';
import { RestaurantBusinessHours } from 'src/entities/restaurantBusinessHour.entity';
import { WorkTime } from 'src/entities/workTime.entity';
import { VietNamTimeZoneNum } from 'src/jobs';
import { RestaurantJobService } from 'src/jobs/restaurants/restaurantJob.service';
import { AwsS3Service } from 'src/providers/aws/awsS3.service';
import { DatabaseService } from 'src/providers/database/database.service';
// import { DynamicLinkService } from '../../dynamicLink/dynamicLink.service';
import { LocationService } from '../../location/location.service';
import { ProvinceService } from '../../province/province.service';
import { RestaurantPaymentMethodsService } from '../../restaurant-payment-methods/restaurant-payment-methods.service';
import { RestaurantCounterService } from '../../restaurantCounter/restaurantCounter.service';
import { RestaurantReviewSummaryService } from '../../restaurantReview/services/restaurantReviewSummary.service';
import { RestaurantUpdateHistoryService } from '../../restaurantUpdateHistory/restaurantUpdateHistory.service';
import {
    CreateRestaurantDto,
    CreateRestaurantLocationDto,
    GetRestaurantListDto,
    GetRestaurantListWithRevenueDto,
    UpdateRestaurantAdminSettingDto,
    UpdateRestaurantDto,
    UpdateRestaurantOnTopDto,
} from '../dto';
import { GetExportRestaurantDto } from '../dto/get-export-restaurant.dto';
import { UpdateImageBackgroundDto } from '../dto/upload-image.dto';
import { RestaurantPublisher } from '../publishers/restaurant.publisher';
import { RestaurantBusinessHourService } from './restaurantBusinessHour.service';
import { RestaurantLocationService } from './restaurantLocation.service';
import { Branch } from 'src/entities/branch.entity';
import { Order, OrderType } from 'src/entities/order.entity';
import { EventService } from 'src/events/event.service';
import { GlobalRestaurantCommandService } from 'src/providers/microservices/deliveryServiceProxy/service/globalRestaurant.service';
import { MerchantHasRoleCommandService } from 'src/providers/microservices/deliveryServiceProxy/service/merchantHasRole.service';
import { EmployeeService } from '../../employee/employee.service';
import { UpdateDataDto } from '../../globalRestaurant/dto/update-data.dto';
import { CentralizedUserService } from '../../user/services/centralizedUser.service';
import { UpdateMerchantDto } from '../dto/update-merchant.dto';
import { RestaurantPromotionV2 } from '../types/restaurantPromotion.interface';

@Injectable()
export class RestaurantService {
    constructor(
        private readonly s3Service: AwsS3Service,
        private readonly restaurantQueue: RestaurantJobService,
        private readonly restaurantReviewSummaryService: RestaurantReviewSummaryService,
        private readonly restaurantBusinessHourService: RestaurantBusinessHourService,
        private readonly restaurantUpdateHistoryService: RestaurantUpdateHistoryService,
        private readonly restaurantLocationService: RestaurantLocationService,
        private readonly provinceService: ProvinceService,
        private readonly locationService: LocationService,
        private readonly restaurantCounterService: RestaurantCounterService,
        // private readonly dynamicLinkService: DynamicLinkService,
        private readonly restaurantPublisher: RestaurantPublisher,
        private readonly restaurantPaymentMethodsService: RestaurantPaymentMethodsService,
        private readonly centralizedUserService: CentralizedUserService,
        private readonly employeeService: EmployeeService,
        private merchantHasRoleService: MerchantHasRoleCommandService,
        private readonly globalRestaurantService: GlobalRestaurantCommandService,
        private eventService: EventService,
    ) {}

    // private async updateRestaurantOwners({ id, users }: Restaurant, userIds: number[], provinceId: string) {
    //     const oldUserIds = users.map(({ id }) => id);
    //     const removedUserIds = _.difference(oldUserIds, userIds);
    //     const insertUserIds = _.difference(userIds, oldUserIds);

    //     await DatabaseService.getEntityManagerByProvinceId(provinceId)
    //         .createQueryBuilder()
    //         .relation(Restaurant, 'users')
    //         .of(id)
    //         .addAndRemove(insertUserIds, removedUserIds);
    // }

    private async updateRestaurantCategories(restaurant: Restaurant, categoryIds: number[], provinceId: string) {
        const { categories } = restaurant;
        const oldCategoryIds = categories.map(({ id }) => id);
        const removedCategoryIds = _.difference(oldCategoryIds, categoryIds);
        const insertCategoryIds = _.difference(categoryIds, oldCategoryIds);
        await DatabaseService.getEntityManagerByProvinceId(provinceId)
            .createQueryBuilder()
            .relation(Restaurant, 'categories')
            .of(restaurant)
            .addAndRemove(insertCategoryIds, removedCategoryIds);
    }

    private async removeAfterAddRestaurantCategories(
        restaurantId: number,
        categoryIds: Array<number> = [],
        provinceId: string,
    ) {
        await DatabaseService.getEntityManagerByProvinceId(provinceId)
            .getRepository(CategoryRestaurant)
            .delete({ restaurant_id: restaurantId });
        await DatabaseService.getEntityManagerByProvinceId(provinceId).save(
            CategoryRestaurant,
            categoryIds.map((id) => ({ category_id: id, restaurant_id: restaurantId })),
        );
    }

    public async exportRestaurantWithMerchant(getExportRestaurantDto: GetExportRestaurantDto, provinceId: string) {
        const { page, limit, from_date, to_date } = getExportRestaurantDto;
        const offset = page * limit;
        let builder = DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).createQueryBuilder(
            'restaurant',
        );
        if (from_date && to_date) {
            builder = builder.andWhere('restaurant.created_at between :start and :end', {
                start: from_date.toDate(),
                end: to_date.toDate(),
            });
        }
        builder = builder
            .leftJoinAndSelect('restaurant.location', 'restaurantLocation')
            .leftJoinAndSelect('restaurantLocation.location', 'ward')
            .leftJoinAndSelect('ward.parent', 'district')
            .leftJoinAndSelect('district.parent', 'city')
            .addSelect(
                (subQuery) =>
                    subQuery
                        .select(`GROUP_CONCAT(DISTINCT collections.name SEPARATOR ', ')`)
                        .from(Restaurant, 'restaurants')
                        .innerJoin('restaurants.categories', 'categories')
                        .innerJoin('categories.collection', 'collections')
                        .where('restaurants.id = restaurant.id')
                        .addGroupBy('restaurants.id'),
                'collections',
            );

        const restaurants = await builder
            .groupBy('restaurant.id')
            .setParameters({
                offset,
                limit,
            })
            .orderBy('restaurant.id', 'ASC')
            .limit(limit)
            .offset(offset)
            .getRawMany();

        if (restaurants.length == 0) {
            return [];
        }
        const result = restaurants;
        const restaurantIds = restaurants.map((restaurant: any) => restaurant.restaurant_id);

        const merchants = await DatabaseService.getRepositoryByDefaultConnection(Merchant)
            .createQueryBuilder('merchant')
            .innerJoinAndSelect(
                'merchant_has_roles',
                'merchantHasRole',
                'merchantHasRole.merchant_id = merchant.id AND merchantHasRole.province_id = :provinceId AND merchantHasRole.restaurant_id IN (:...restaurantIds)',
                {
                    provinceId,
                    restaurantIds,
                },
            )
            // .innerJoinAndSelect(
            //     'merchant_provinces',
            //     'merchantProvince',
            //     'merchantProvince.merchant_id = merchant.id AND merchantProvince.province_id = :provinceId',
            //     {
            //         provinceId,
            //     },
            // )
            // .innerJoinAndSelect(
            //     'merchant_restaurants',
            //     'merchantRestaurant',
            //     'merchantRestaurant.merchant_province_id = merchantProvince.id AND merchantRestaurant.province_id = :provinceId',
            //     {
            //         provinceId,
            //     },
            // )
            // .where('merchantRestaurant.restaurant_id IN (:...restaurantIds)', { restaurantIds })
            .getRawMany();

        return result.map((restaurant) => {
            const merchant = merchants.find((merchant) => {
                return restaurant.restaurant_id === merchant.merchantHasRole_restaurant_id;
            });
            return { ...restaurant, ...merchant };
        });
    }

    public async getRestaurantList(query: GetRestaurantListDto, provinceId: string): Promise<[Restaurant[], number]> {
        const {
            orderBy,
            sortedBy,
            limit,
            page,
            from_date,
            to_date,
            search,
            openTime,
            closeTime,
            onTop,
            star,
            cooperating,
            status,
            operatingStatus,
            approval_status,
            freeship,
            mondoc,
            buoisang,
            buoitrua,
            buoitoi,
            on_top,
            long_preparing,
            top_trending,
            top,
            mid_afternoon,
            frame,
            newsfeed_ads,
            provinceId: channelId,
            collectionId,
            adCateId,
            rating_from,
            rating_to,
            trade_discount,
            trade_discount_condition,
            promotion_id,
            vill_ad_id,
            res_ads_statuses,
            categoryIds,
            is_trial,
            has_seller,
            seller_id,
            branch_id,
        } = query;
        const builder = DatabaseService.getEntityManagerByProvinceId(provinceId)
            .getRepository(Restaurant)
            .createQueryBuilder('restaurant');

        if (from_date && to_date) {
            builder.andWhere(
                `restaurant.created_at BETWEEN '${from_date.toISOString()}' AND '${to_date.toISOString()}'`,
            );
            builder.andWhere(
                `restaurant.created_at BETWEEN '${from_date.toISOString()}' AND '${to_date.toISOString()}'`,
            );
        }
        if (!_.isEmpty(search)) {
            builder.andWhere('( restaurant.name LIKE :searchItem OR restaurant.code LIKE :searchItem)', {
                searchItem: `%${search}%`,
            });
        }

        if (openTime) {
            builder.andWhere('restaurant.time_open >= :openTime', { openTime: _.toNumber(openTime) });
        }
        if (closeTime) {
            builder.andWhere('restaurant.time_close <= :closeTime', { closeTime: _.toNumber(closeTime) });
        }
        if (onTop) {
            builder.andWhere('restaurant.on_top = :onTop', { onTop: _.toNumber(onTop) });
        }
        if (star) {
            builder.andWhere('restaurant.star = :star', { star: _.toNumber(star) });
        }
        if (cooperating) {
            builder.andWhere('restaurant.cooperating = :cooperating', { cooperating: _.toNumber(cooperating) });
        }
        if (status) {
            builder.andWhere('restaurant.status = :status', { status: _.toNumber(status) });
        }
        if (operatingStatus) {
            builder.andWhere('restaurant.operating_status = :operatingStatus', { operatingStatus });
        }
        if (approval_status) {
            builder.andWhere('restaurant.approval_status = :approval_status', { approval_status });
        }
        if (freeship) {
            builder.andWhere('restaurant.freeship = :freeship', { freeship });
        }
        if (mondoc) {
            builder.andWhere('restaurant.mondoc = :mondoc', { mondoc });
        }
        if (buoisang) {
            builder.andWhere('restaurant.buoisang = :buoisang', { buoisang });
        }
        if (buoitrua) {
            builder.andWhere('restaurant.buoitrua = :buoitrua', { buoitrua });
        }
        if (buoitoi) {
            builder.andWhere('restaurant.buoitoi = :buoitoi', { buoitoi });
        }
        if (on_top) {
            builder.andWhere('restaurant.on_top = :on_top', { on_top });
        }
        if (is_trial) {
            builder.andWhere('restaurant.is_trial = :is_trial', { is_trial });
        }
        if (long_preparing) {
            builder.andWhere('restaurant.long_preparing = :long_preparing', { long_preparing });
        }
        if (top_trending) {
            builder.andWhere('restaurant.top_trending = :top_trending', { top_trending });
        }
        if (top) {
            builder.andWhere('restaurant.top = :top', { top });
        }
        if (mid_afternoon) {
            builder.andWhere('restaurant.mid_afternoon = :mid_afternoon', { mid_afternoon });
        }
        if (!_.isNil(frame)) {
            builder.andWhere('restaurant.frame_id = :frame', { frame });
        }
        if (!_.isNil(channelId)) {
            builder.andWhere('restaurant.province_id = :channelId', { channelId });
        }
        if (!_.isNil(newsfeed_ads)) {
            builder.andWhere('restaurant.newsfeed_ads = :newsfeed_ads', { newsfeed_ads });
        }
        if (has_seller) {
            if (has_seller == 1) {
                builder.andWhere('restaurant.seller_id is not null');
            } else {
                builder.andWhere('restaurant.seller_id is null');
            }
        }

        if (adCateId) {
            builder.innerJoin(
                'restaurant.restaurantAds',
                'ads',
                `ads.ad_cate_id = :adCateId AND ads.is_active = 1 AND ((ads.active_from IS NULL AND ads.active_to IS NULL) OR (ads.active_from < current_timestamp() AND ads.active_to > current_timestamp()))`,
                {
                    adCateId,
                },
            );
        }

        if (!_.isNil(rating_from)) {
            builder.andWhere('restaurant.rating >= :rating_from', { rating_from });
        }

        if (!_.isNil(rating_to)) {
            builder.andWhere('restaurant.rating <= :rating_to', { rating_to });
        }

        if (!_.isNil(promotion_id)) {
            builder.innerJoin('restaurant.promotions', 'promotions', 'promotions.id = :promotionId', {
                promotionId: promotion_id,
            });
        }

        if (!_.isNil(vill_ad_id) || (res_ads_statuses && res_ads_statuses.length)) {
            let conditions: string;
            if (!_.isNil(vill_ad_id)) {
                conditions = `restaurantAds.vill_ad_id = ${vill_ad_id}`;
            }
            if (res_ads_statuses && res_ads_statuses.length) {
                const con = `'${res_ads_statuses.join(`','`)}'`;
                conditions = conditions
                    ? conditions + ` AND restaurantAds.status IN (${con})`
                    : `restaurantAds.status IN (${con})`;
            }
            builder.innerJoin('restaurant.restaurantAds', 'restaurantAds', conditions);
        }

        if (!_.isNil(trade_discount_condition) && !_.isNil(trade_discount)) {
            builder.andWhere(`restaurant.trade_discount ${trade_discount_condition} :trade_discount`, {
                trade_discount,
            });
        }
        builder.leftJoinAndSelect('restaurant.province', 'province');
        builder.leftJoinAndSelect('restaurant.categories', 'category');
        builder.leftJoinAndSelect('restaurant.businessHours', 'businessHours');
        builder.leftJoinAndSelect('businessHours.workTimes', 'workTimes');

        if (collectionId) {
            builder.innerJoin('category.collection', 'collection');
            builder.andWhere('collection.id = :collectionId', { collectionId });
        }

        if (categoryIds) {
            builder.andWhere('category.id IN (:...categoryIds)', { categoryIds });
        }
        if (seller_id) {
            builder.andWhere('restaurant.seller_id = :seller_id', { seller_id });
        }

        if (branch_id) {
            builder.andWhere('restaurant.branch_id = :branch_id', { branch_id });
        }

        const result = await builder
            .orderBy({
                [`restaurant.${orderBy}`]: sortedBy,
            })
            .skip(page * limit)
            .take(limit)
            .getManyAndCount();

        return result;
    }

    public async findOneById(id: number, provinceId: string): Promise<Restaurant> {
        if (!id) return null;
        return DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
            where: { id },
        });
    }

    public async getRestaurantByIdWithFullDetails(id: number, provinceId: string): Promise<Restaurant> {
        const restaurant = await DatabaseService.getEntityManagerByProvinceId(provinceId)
            .createQueryBuilder(Restaurant, 'restaurant')
            .leftJoinAndSelect('restaurant.location', 'restaurantLocation')
            // .leftJoinAndSelect('restaurant.users', 'users')
            .leftJoinAndSelect('restaurant.categories', 'categories')
            .leftJoinAndSelect('restaurant.businessHours', 'businessHours')
            .leftJoinAndSelect('businessHours.workTimes', 'workTimes')
            .leftJoinAndSelect('restaurant.frame', 'frame')
            .leftJoinAndSelect('restaurant.paymentMethods', 'paymentMethods')
            .leftJoinAndSelect('restaurant.branch', 'branch')
            // .leftJoinAndSelect('restaurant.ads_campaigns', 'ads_campaigns')
            // .leftJoinAndSelect('ads_campaigns.ads_items', 'ads_items')
            // .leftJoinAndSelect('ads_items.adCategory', 'adCategory')
            .where('restaurant.id = :id', { id })
            .getOne();

        if (!restaurant) throw new NotFoundException('Restaurant not found');

        if (restaurant?.location?.location_id) {
            restaurant.location.location = await this.locationService.findOne(restaurant.location.location_id);
        }

        if (restaurant.seller_id) {
            const seller = await this.employeeService.getEmployeeById(restaurant.seller_id);
            restaurant.seller = seller;
        }

        return restaurant;
    }

    public async findByIds(ids: number[], provinceId: string): Promise<Restaurant[]> {
        if (_.isEmpty(ids)) return [];
        return DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
            .createQueryBuilder('restaurant')
            .where('restaurant.id IN (:...ids)', { ids })
            .getMany();
    }

    public async uploadImage(
        restaurantId: number,
        file: Express.Multer.File,
        authorId: number,
        provinceId: string,
        body: UpdateImageBackgroundDto,
    ): Promise<Restaurant> {
        const { isBackground } = body;
        const restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
            where: { id: restaurantId },
        });
        if (!restaurant) throw new NotFoundException('Restaurant not found');

        const fileName = createImageUrlDependingOnProvince(
            restaurantId,
            file.originalname.split('.').pop(),
            provinceId,
            'RESTAURANT',
        );

        const data = await this.s3Service.uploadPublicFile(fileName, file.mimetype, file.buffer, undefined, true, true);
        const thumbnail = await this.s3Service.uploadPublicFile(
            'thumbnail/' + fileName,
            file.mimetype,
            file.buffer,
            300,
            true,
            true,
        );
        const { Location, Key } = data;
        if (isBackground) {
            if (restaurant.background) {
                const oldKey = new URL(restaurant.background).pathname.replace('/', '').trim();
                if (!_.isEqual(Key, oldKey)) {
                    this.s3Service.deletePublicFile(oldKey);
                }
            }
            restaurant.background = Location;
        } else {
            if (restaurant.image) {
                const oldKey = new URL(restaurant.image).pathname.replace('/', '').trim();
                if (!_.isEqual(Key, oldKey)) {
                    this.s3Service.deletePublicFile(oldKey);
                }
            }
            restaurant.image = Location;
            if (restaurant.thumbnails) {
                const oldKey = new URL(restaurant.thumbnails).pathname.replace('/', '').trim();
                if (!_.isEqual(thumbnail.Key, oldKey)) {
                    this.s3Service.deletePublicFile(oldKey);
                }
            }
            restaurant.thumbnails = thumbnail.Location;
        }

        const old_data = await this.getRestaurantByIdWithFullDetails(restaurantId, provinceId);
        const result = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).save(restaurant);
        const new_data = await this.getRestaurantByIdWithFullDetails(restaurantId, provinceId);
        this.restaurantUpdateHistoryService.createUpdateHistory(restaurantId, authorId, old_data, new_data, provinceId);
        // const updateGlobalData = new UpdateDataDto();
        // Object.assign(updateGlobalData, new_data);
        // updateGlobalData.restaurant_id = restaurantId;
        // await this.globalRestaurantService.update(restaurantId, provinceId, updateGlobalData);
        this.restaurantPublisher.publishUpdatingEvent(new_data, provinceId);
        this.restaurantQueue.reIndexRestaurantToEls(restaurant.id, provinceId);
        return result;
    }

    public async checkOwner() {
        return;
    }

    public async updateRestaurant(
        id: number,
        body: UpdateRestaurantDto,
        userId: number,
        provinceId: string,
    ): Promise<Restaurant> {
        const restaurant = await this.getRestaurantByIdWithFullDetails(id, provinceId);
        if (!restaurant) throw new NotFoundException('Khong tim thay cua hang!');
        const oldData = { ...restaurant };
        const {
            information,
            name,
            address,
            location,
            // users,
            categories,
            deliveryFee,
            phone,
            mobile,
            latitude,
            longitude,
            // openTime,
            // closeTime,
            description,
            star,
            freeship,
            top,
            mondoc,
            buoisang,
            buoitoi,
            buoitrua,
            cooperating,
            status,
            top_trending,
            businessHours,
            operatingStatus,
            frameId,
            code,
            approval_status,
            reopenTime,
            long_preparing,
            mid_afternoon,
            newsfeed_ads,
            provinceId: channelId,
            is_trial,
            branchId,
            address_note,
        } = body;

        // let isApprovalStatusChanged = false;
        const updateData: Partial<Restaurant> = {};
        if (!_.isUndefined(name)) {
            updateData.name = name;
        }
        if (!_.isUndefined(address)) {
            updateData.address = address;
        }
        if (!_.isUndefined(deliveryFee)) {
            updateData.delivery_fee = deliveryFee;
        }
        if (!_.isUndefined(phone)) {
            updateData.phone = phone;
        }
        if (!_.isUndefined(mobile)) {
            updateData.mobile = mobile;
        }
        if (!_.isUndefined(latitude)) {
            updateData.latitude = latitude;
        }
        if (!_.isUndefined(longitude)) {
            updateData.longitude = longitude;
        }
        /* if (!_.isUndefined(openTime)) {
            updateData.time_open = openTime;
        }
        if (!_.isUndefined(closeTime)) {
            updateData.time_close = closeTime;
        } */
        if (!_.isUndefined(star)) {
            updateData.star = star;
        }
        if (!_.isUndefined(freeship)) {
            updateData.freeship = freeship;
        }
        if (!_.isUndefined(top)) {
            updateData.top = top;
        }
        if (!_.isUndefined(mondoc)) {
            updateData.mondoc = mondoc;
        }
        if (!_.isUndefined(buoisang)) {
            updateData.buoisang = buoisang;
        }
        if (!_.isUndefined(buoitoi)) {
            updateData.buoitoi = buoitoi;
        }
        if (!_.isUndefined(buoitrua)) {
            updateData.buoitrua = buoitrua;
        }
        if (!_.isUndefined(cooperating)) {
            updateData.cooperating = cooperating;
        }
        if (!_.isUndefined(top_trending)) {
            updateData.top_trending = top_trending;
        }
        if (!_.isUndefined(is_trial)) {
            updateData.is_trial = is_trial;
        }
        if (!_.isUndefined(status)) {
            updateData.status = status;
        }
        if (!_.isUndefined(operatingStatus)) {
            this.eventService.restaurantOperationStatusChangeEvent(restaurant.id, operatingStatus, +provinceId);
            updateData.operating_status = operatingStatus;
        }
        if (!_.isUndefined(code)) {
            updateData.code = code;
        }
        if (!_.isUndefined(long_preparing)) {
            updateData.long_preparing = long_preparing;
        }
        if (!_.isUndefined(newsfeed_ads)) {
            updateData.newsfeed_ads = newsfeed_ads;
        }
        if (reopenTime && updateData.operating_status == ERestaurantOperatingStatus.TEMPT_CLOSE) {
            updateData.reopen_time = new Date(reopenTime);
        } else {
            updateData.reopen_time = null;
        }
        if (!_.isNil(channelId)) {
            const validChannelId = await this.provinceService.validateSubProvince(+provinceId, channelId);
            if (validChannelId) {
                updateData.province_id = channelId;
            } else {
                throw new BadRequestException('Nhà hàng không thuộc thị trường này');
            }
        }
        if (!_.isUndefined(frameId)) {
            if (!_.isNil(frameId)) {
                const frame = await DatabaseService.getEntityManagerByProvinceId(provinceId).findOne(Frame, {
                    where: { id: frameId },
                });
                if (!frame) throw new BadRequestException('Không tìm thấy khung ảnh!');
            }
            updateData.frame_id = frameId;
        }
        if (!_.isUndefined(description)) updateData.description = description;
        if (!_.isUndefined(information)) updateData.information = information;
        if (!_.isUndefined(approval_status)) {
            // if (approval_status != restaurant.approval_status) {
            //     isApprovalStatusChanged = true;
            // }
            if (
                approval_status == ERestaurantApprovalStatus.APPROVED &&
                approval_status != restaurant.approval_status
            ) {
                const merchant = await lastValueFrom(this.merchantHasRoleService.getOwner(id, Number(provinceId)));
                if (!merchant) {
                    throw new BadRequestException('Nhà hàng cần phải thêm chủ quán trước khi duyệt!');
                }
                console.log(merchant);
            }
            updateData.approval_status = approval_status;
        }
        if (!_.isNil(mid_afternoon)) {
            updateData.mid_afternoon = mid_afternoon;
        }
        if (branchId) {
            if (!_.isNil(branchId)) {
                const branch = await DatabaseService.getEntityManagerByProvinceId(provinceId).findOne(Branch, {
                    where: { id: branchId },
                });
                if (!branch) throw new BadRequestException('Không tìm thấy branch!');
            }
            updateData.branch_id = branchId;
        }

        console.log(address_note);

        if (_.isUndefined(address_note)) {
            updateData.address_note = null;
        } else {
            updateData.address_note = address_note;
        }

        await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
            .createQueryBuilder()
            .update()
            .set(updateData)
            .where('id = :restaurantId', { restaurantId: id })
            .execute();

        // if (!_.isUndefined(users)) {
        //     await this.updateRestaurantOwners(restaurant, users, provinceId);
        // }
        if (!_.isUndefined(categories)) {
            await this.updateRestaurantCategories(restaurant, categories, provinceId);
        }

        if (businessHours) {
            const workItemIds = [];
            _.map(businessHours, 'workTimes').forEach((arr) => {
                const ids = _.map(arr, 'id').filter((id) => !_.isNil(id));
                workItemIds.push(...ids);
            });

            const currentWorkTimes = await DatabaseService.getEntityManagerByProvinceId(provinceId)
                .getRepository(WorkTime)
                .createQueryBuilder('worktime')
                .innerJoin('worktime.businessHour', 'businessHour', `businessHour.restaurant_id = ${id}`)
                .getMany();
            const delWorkTimeIds = _.difference(_.map(currentWorkTimes, 'id'), workItemIds);

            if (!_.isEmpty(delWorkTimeIds)) {
                await DatabaseService.getEntityManagerByProvinceId(provinceId)
                    .getRepository(WorkTime)
                    .createQueryBuilder()
                    .delete()
                    // .where(`id NOT IN (${workItemIds.join(',')})`)
                    .andWhereInIds(delWorkTimeIds)
                    .execute();
            }
            restaurant.businessHours = await this.restaurantBusinessHourService.updateBusinessHours(
                DatabaseService.getEntityManagerByProvinceId(provinceId),
                restaurant.id,
                businessHours,
            );
            restaurant.is_open = await this.updateRestaurantOpeningStatusByBusinessHours(
                restaurant.id,
                restaurant.businessHours,
                provinceId,
            );
        }

        if (location?.locationId && location.street) {
            if (restaurant.location) {
                await this.restaurantLocationService.update(restaurant.id, location, provinceId);
            } else {
                const createRestaurantLocation: CreateRestaurantLocationDto = {
                    restaurantId: restaurant.id,
                    locationId: location.locationId,
                    street: location.street,
                };
                await this.restaurantLocationService.create(createRestaurantLocation, provinceId);
            }
        }

        const result = await this.getRestaurantByIdWithFullDetails(id, provinceId);
        await this.restaurantPublisher.publishUpdatingEvent(result, provinceId);
        if (result) {
            this.restaurantPublisher.publishRestaurantUpdatedEvent(result, +provinceId);
        }
        if (result && result.reopen_time && result.operating_status == ERestaurantOperatingStatus.TEMPT_CLOSE) {
            this.restaurantQueue.addJobToReopenRestaurant(result.id, provinceId);
        } else if (restaurant.reopen_time && !result.reopen_time) {
            this.restaurantQueue.removeReopenTimeJob(result.id, provinceId);
        }
        this.restaurantQueue.reIndexRestaurantToEls(result.id, provinceId);
        // if (isApprovalStatusChanged) {
        //     this.sendNotiToMerchantWhenApprovalStatusChange(id, approval_status, provinceId);
        // }

        oldData.businessHours = oldData.businessHours.map((item) => {
            return _.omit(item, ['updated_at']);
        }) as any[];
        const newData = { ...result };
        newData.businessHours = newData.businessHours.map((item) => {
            return _.omit(item, ['updated_at']);
        }) as any[];

        if (
            !_.isEqual(
                _.omit(oldData, ['updated_at', 'on_top', 'location']),
                _.omit(newData, ['updated_at', 'on_top', 'location']),
            )
        ) {
            this.restaurantUpdateHistoryService.createUpdateHistory(id, userId, oldData, newData, provinceId);
        }
        if (result.location && location.locationId) {
            result.location.location = await this.locationService.findOne(location.locationId);
        }
        // const updateGlobalData = new UpdateDataDto();
        // Object.assign(updateGlobalData, result);
        // updateGlobalData.restaurant_id = id;
        // this.globalRestaurantService.update(restaurant.id, provinceId, updateGlobalData);
        return result;
    }

    async updateRestaurantAdminSetting(
        {
            tradeDiscountType,
            tradeDiscount,
            tradeDiscountPeriodType,
            ad_promo,
            roundTradeDiscount,
        }: UpdateRestaurantAdminSettingDto,
        id: number,
        authorId: number,
        provinceId: string,
    ) {
        const restaurant = await this.getRestaurantByIdWithFullDetails(id, provinceId);
        if (!restaurant) throw new NotFoundException('Không tìm thấy nhà hàng!');
        await DatabaseService.getEntityManagerByProvinceId(provinceId).update(Restaurant, id, {
            trade_discount_type: tradeDiscountType,
            trade_discount: tradeDiscount,
            trade_discount_period_type: tradeDiscountPeriodType,
            ad_promo,
            round_trade_discount: roundTradeDiscount,
        });

        this.restaurantQueue.reIndexRestaurantToEls(id, provinceId);
        const restaurantUpdated = await this.getRestaurantByIdWithFullDetails(id, provinceId);
        this.restaurantUpdateHistoryService.createUpdateHistory(
            restaurant.id,
            authorId,
            restaurant,
            restaurantUpdated,
            provinceId,
        );
        // const updateGlobalData = new UpdateDataDto();
        // Object.assign(updateGlobalData, restaurantUpdated);
        // updateGlobalData.restaurant_id = id;
        // this.globalRestaurantService.update(restaurant.id, provinceId, updateGlobalData);
        this.restaurantPublisher.publishUpdatingEvent(restaurantUpdated, provinceId);
        return restaurantUpdated;
    }
    async updateRestaurantOnTop({ onTop }: UpdateRestaurantOnTopDto, id: number, authorId: number, provinceId: string) {
        const restaurant = await this.getRestaurantByIdWithFullDetails(id, provinceId);
        if (!restaurant) throw new NotFoundException('Không tìm thấy nhà hàng!');
        await DatabaseService.getEntityManagerByProvinceId(provinceId).update(Restaurant, id, {
            on_top: onTop,
        });

        this.restaurantQueue.reIndexRestaurantToEls(id, provinceId);
        const restaurantUpdated = await this.getRestaurantByIdWithFullDetails(id, provinceId);
        if (restaurant.on_top != restaurantUpdated.on_top) {
            this.restaurantUpdateHistoryService.createUpdateHistory(
                restaurant.id,
                authorId,
                restaurant,
                restaurantUpdated,
                provinceId,
            );
        }

        return restaurantUpdated;
    }

    checkRestaurantOpeningStatusByBusinessHoursAndCurrentTime(
        businessHours: RestaurantBusinessHours[],
        provinceId: string,
    ) {
        const currentIsoWeekDay = moment().utcOffset(VietNamTimeZoneNum).isoWeekday();
        const currentHourTime = moment().utcOffset(VietNamTimeZoneNum).format('HH:mm');
        return isOpenByBusinessHours(businessHours, currentIsoWeekDay, currentHourTime);
    }

    async updateRestaurantOpeningStatusByBusinessHours(
        restaurantId: number,
        businessHours: RestaurantBusinessHours[],
        provinceId: string,
    ) {
        const isOpen = await this.checkRestaurantOpeningStatusByBusinessHoursAndCurrentTime(businessHours, provinceId);
        const updateResult = await DatabaseService.getEntityManagerByProvinceId(provinceId)
            .getRepository(Restaurant)
            .update(restaurantId, { is_open: isOpen });
        if (updateResult.affected > 0) {
            return isOpen;
        } else {
            throw new Error('Lỗi trong quá trình cập nhật tình trạng mở cửa của quán');
        }
    }

    public async createRestaurant(
        body: CreateRestaurantDto,
        authorId: number,
        provinceId: string,
    ): Promise<Restaurant> {
        const {
            name,
            address,
            location,
            // users,
            categories,
            deliveryFee,
            phone,
            mobile,
            latitude,
            longitude,
            /* openTime,
            closeTime, */
            description,
            information,
            tradeDiscountType,
            tradeDiscount,
            star,
            freeship,
            top,
            mondoc,
            buoisang,
            buoitoi,
            buoitrua,
            cooperating,
            onTop,
            couponType,
            coupon,
            couponOrder,
            couponValue,
            couponExp,
            status,
            couponTime,
            top_trending,
            tradeDiscountPeriodType,
            operatingStatus,
            businessHours,
            code,
            approval_status,
            long_preparing,
            restaurant_province_id,
            is_trial,
            paymentMethods,
            branchId,
            address_note,
        } = body;

        const employee = await this.employeeService.getEmployeeById(authorId);
        let restaurant = new Restaurant({
            name,
            address,
            delivery_fee: deliveryFee,
            phone,
            mobile,
            latitude,
            longitude,
            trade_discount_type: tradeDiscountType,
            trade_discount: tradeDiscount,
            trade_discount_period_type: tradeDiscountPeriodType,
            star,
            freeship,
            top,
            mondoc,
            buoisang,
            buoitoi,
            buoitrua,
            cooperating,
            on_top: onTop,
            is_trial,
            coupon_type: couponType,
            status,
            // users: [],
            categories: [],
            created_at: new Date(),
            updated_at: new Date(),
            top_trending,
            operating_status: operatingStatus,
            description,
            information,
            coupon,
            coupon_order: couponOrder,
            coupon_value: couponValue,
            coupon_exp: couponExp,
            coupon_time: couponTime,
            code,
            approval_status,
            long_preparing,
            province_id: +restaurant_province_id,
            branch_id: branchId,
            author_id: authorId,
            author: employee,
            address_note,
        });

        // if (users) {
        //     restaurant.users = await DatabaseService.getEntityManagerByProvinceId(provinceId).find(User, {
        //         where: { id: In(users) },
        //     });
        // }
        restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).manager.transaction(
            async (entityManager) => {
                const newRestaurant = await entityManager.save(Restaurant, restaurant);
                if (!newRestaurant) {
                    throw new HttpException('Lỗi trong quá trình tạo nhà hàng', HttpStatus.BAD_REQUEST);
                }
                const restaurantReviewSummary = await this.restaurantReviewSummaryService.createWithTransactionWrapper(
                    entityManager,
                    newRestaurant.id,
                );
                if (!restaurantReviewSummary) {
                    throw new HttpException('Lỗi trong quá trình tạo nhà hàng', HttpStatus.BAD_REQUEST);
                }
                newRestaurant.restaurantReviewSumary = restaurantReviewSummary;

                if (paymentMethods && paymentMethods.length > 0) {
                    newRestaurant.paymentMethods =
                        await this.restaurantPaymentMethodsService.createWithTransactionWrapper(
                            entityManager,
                            restaurant.id,
                            paymentMethods,
                        );
                }
                return newRestaurant;
            },
        );
        // add categories
        if (categories) {
            const ids = _.map(
                await DatabaseService.getEntityManagerByProvinceId(provinceId).find(Category, {
                    where: { id: In(categories) },
                }),
                'id',
            );
            this.removeAfterAddRestaurantCategories(restaurant.id, ids, provinceId);
        }

        if (location.locationId && location.street) {
            restaurant.location = await this.restaurantLocationService.create(
                {
                    restaurantId: restaurant.id,
                    locationId: location.locationId,
                    street: location.street,
                },
                provinceId,
            );
            restaurant.location.location = await this.locationService.findOne(location.locationId);
        }

        restaurant.businessHours = await this.restaurantBusinessHourService.createBusinessHours(
            DatabaseService.getEntityManagerByProvinceId(provinceId),
            restaurant.id,
            businessHours,
        );
        restaurant.is_open = await this.updateRestaurantOpeningStatusByBusinessHours(
            restaurant.id,
            restaurant.businessHours,
            provinceId,
        );

        const result = await this.getRestaurantByIdWithFullDetails(restaurant.id, provinceId);
        this.restaurantUpdateHistoryService.createUpdateHistory(restaurant.id, authorId, null, result, provinceId);
        // this.restaurantSearchService.indexRestaurant(result.id, provinceId);
        if (restaurant) {
            this.restaurantPublisher.publishUpdatingEvent(restaurant, provinceId);
            this.restaurantPublisher.publishRestaurantCreatedEvent(restaurant, +provinceId);
        }
        return result;
    }

    public async deleteRestaurant(id: number, provinceId: string): Promise<any> {
        const result = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).softDelete(id);
        // this.restaurantSearchService.deleteRestaurant(id, provinceId);
        this.restaurantQueue.reIndexRestaurantToEls(id, provinceId);
        this.restaurantPublisher.publishDeleteRestaurantsEvent(id, provinceId);
        return result;
    }

    // TODO: fix it
    async updatePrefixCodeRestaurants(oldPrefix: string, newCounterId: number, provinceId: string): Promise<any> {
        const restaurantCounter = await this.restaurantCounterService.getById(newCounterId, provinceId);
        if (!restaurantCounter) {
            throw new HttpException('Counter không hợp lệ', HttpStatus.BAD_REQUEST);
        }
        if (oldPrefix === restaurantCounter.prefix) {
            throw new HttpException('Prefix không hợp lệ', HttpStatus.BAD_REQUEST);
        }
        const endOfPrefix = oldPrefix.slice(-1);
        if (restaurantCounter && endOfPrefix == '-') {
            const restaurants = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
                .createQueryBuilder()
                .where(`code LIKE '${oldPrefix}%'`)
                .getMany();
            const updateData: UpdateDataDto[] = [];
            const ids = restaurants.map((e) => e.id);
            if (ids.length > 0) {
                const results = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
                    .createQueryBuilder()
                    .select('code')
                    .update()
                    .set({
                        code: () => {
                            return `REPLACE(code, '${oldPrefix}', '${restaurantCounter.prefix}')`;
                        },
                    })
                    .where(`id In(${ids})`)
                    .execute();
                const lastData = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
                    .createQueryBuilder()
                    .where(`id In(${ids})`)
                    .getMany();
                lastData.map((e) => {
                    const tempData = new UpdateDataDto();
                    Object.assign(tempData, e);
                    tempData.restaurant_id = e.id;
                    tempData.reopen_time = e.reopen_time ? moment(e.reopen_time).format('YYYY-MM-DD HH:mm:ss') : null;
                    updateData.push(tempData);
                });
                this.restaurantPublisher.publishUpdateRestaurantsEvent(updateData, provinceId);
                return {
                    number_restaurant: results.affected,
                };
            }
        } else {
            throw new HttpException('Prefix không hợp lệ', HttpStatus.BAD_REQUEST);
        }
    }

    async generateCode(provinceId: string) {
        const appSettingRepo = DatabaseService.getRepositoryByProvinceId(AppSetting, provinceId);
        const code = await appSettingRepo.find({
            where: [{ key: EAppSettingKey.RESTAURANT_CODE_CONF }, { key: EAppSettingKey.RESTAURANT_CODE_COUNTER }],
        });
        if (code && code.length >= 2) {
            const configData = code.find(({ key }) => key == EAppSettingKey.RESTAURANT_CODE_CONF);
            const config: IRestaurantCodeConfig = configData ? JSON.parse(configData.value) : undefined;
            let counter = code.find(({ key }) => key == EAppSettingKey.RESTAURANT_CODE_COUNTER)?.value;
            let prefix = 'MBL-',
                suffixLength = 5;
            if (config) {
                prefix = config.prefix;
                suffixLength = config.suffixLength;
            }
            let data = prefix + String(counter).padStart(suffixLength, '0');
            const isExist = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).count({
                where: { code: data },
            });
            if (!_.isNil(isExist) && isExist) {
                counter = (_.toNumber(counter) + 1).toString();
                await appSettingRepo.update({ key: EAppSettingKey.RESTAURANT_CODE_COUNTER }, { value: counter });
                data = prefix + String(counter).padStart(suffixLength, '0');
            }

            return data;
        } else {
            let base = 'MBL-';
            switch (provinceId) {
                case '1': {
                    base = 'MBL-';
                    break;
                }
                case '2': {
                    base = 'MBR-';
                    break;
                }
                case '3': {
                    base = 'MDL-';
                    break;
                }
                case '4': {
                    base = 'MDT-';
                    break;
                }
                case '5': {
                    base = 'MDX-';
                    break;
                }
                case '6': {
                    base = 'MTN-';
                    break;
                }
                case '7': {
                    base = 'MCL-';
                    break;
                }
                case '8': {
                    base = 'MTA-';
                    break;
                }
                case '11': {
                    base = 'MTV-';
                    break;
                }
                case '12': {
                    base = 'MBA-';
                    break;
                }
                case '13': {
                    base = 'MST-';
                    break;
                }
                case '14': {
                    base = 'MMT-';
                    break;
                }
                case '15': {
                    base = 'MLX-';
                    break;
                }
                case '16': {
                    base = 'MVL-';
                    break;
                }
                case '17': {
                    base = 'MRG-';
                    break;
                }
                case '18': {
                    base = 'MLT-';
                    break;
                }
            }

            let number = 1;
            const restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
                .createQueryBuilder('restaurant')
                .where('restaurant.code IS NOT NULL')
                .orderBy('restaurant.code', 'DESC')
                .getOne();

            if (restaurant && restaurant.code) {
                number = +restaurant.code.split('-')[1];
                number++;
            }
            return `${base}${('00000' + number).slice(('' + number).length)}`;
        }
    }

    async updateSellerForRestaurant(restaurant_id: number, provinceId: string, seller_id: number) {
        await lastValueFrom(this.merchantHasRoleService.checkExistedMerchant(restaurant_id, Number(provinceId)));
        const restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
            where: { id: restaurant_id },
        });
        if (!restaurant) throw new BadRequestException('Không tìm thấy nhà hàng');
        const sellerId = seller_id ? seller_id : null;

        // await this.globalRestaurantService.addSellerForRestaurant(restaurant_id, Number(provinceId), seller_id);
        this.restaurantPublisher.publishSellerRestaurantEvent(restaurant_id, provinceId, seller_id);

        const updateResult = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).update(
            { id: restaurant_id },
            { seller_id: sellerId },
        );
        return updateResult;
    }

    async deleteSellerForRestaurant(restaurant_id: number, provinceId: string) {
        const restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
            where: { id: restaurant_id },
        });
        if (!restaurant) throw new BadRequestException('Không tìm thấy nhà hàng');

        this.restaurantPublisher.publishSellerRestaurantEvent(restaurant_id, provinceId, null);
        // await this.globalRestaurantService.addSellerForRestaurant(restaurant_id, Number(provinceId), null);

        const updateResult = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).update(
            { id: restaurant_id },
            { seller_id: null },
        );
        return updateResult;
    }

    async updateSellerForRestaurants(from: number, to: number) {
        this.restaurantPublisher.publishSellersEvent(from, to);
        const repositories = DatabaseService.getRepositoriesAndNamesOfAllConnections(Restaurant);
        let updateResult = 0;
        for (let i = 0; i < repositories.length; i++) {
            const result = await repositories[i].repository.update({ seller_id: from }, { seller_id: to });
            updateResult = updateResult + (result.affected ?? 0);
        }
        return updateResult;
    }

    /*     async generateDynamicLink(restaurantId: number, provinceId: string) {
        const restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
            where: { id: restaurantId },
        });
        if (!restaurant) throw new BadRequestException('Không tìm thấy nhà hàng');
        const path = `details?id=${restaurantId}&province_id=${provinceId}`;
        const url = await this.dynamicLinkService.createDynamicLinkByFirebase(path, {
            socialTitle: restaurant.name,
            socialDescription: restaurant.description.replace(/<[^>]*(>|$)|&nbsp;|&zwnj;|&raquo;|&laquo;|&gt;/g, ''),
            socialImageLink: restaurant.image ?? restaurant.thumbnails,
        });
        const old_data = await this.dynamicLinkService.createDynamicLinkByFirebase(path, {
            socialTitle: restaurant.name,
            socialDescription: restaurant.description.replace(/<[^>]*(>|$)|&nbsp;|&zwnj;|&raquo;|&laquo;|&gt;/g, ''),
            socialImageLink: restaurant.image ?? restaurant.thumbnails,
        });
        return {
            shortLink: url.shortLink,
            oldLink: old_data.shortLink,
        };
    } */

    // async getMerchantDeviceTokensOfRestaurant(restaurantId: number, provinceId: string) {
    //     const restaurant = await DatabaseService.getEntityManagerByProvinceId(provinceId)
    //         .getRepository(Restaurant)
    //         .createQueryBuilder('restaurant')
    //         .leftJoinAndSelect('restaurant.users', 'users')
    //         .where('restaurant.id = :restaurantId', { restaurantId })
    //         .getOne();

    //     const deviceTokens = restaurant.users
    //         ?.map(({ device_token }) => device_token)
    //         .filter((deviceToken) => !!deviceToken);

    //     return {
    //         deviceTokens: [],
    //         restaurant,
    //     };
    // }

    // async sendNotiToMerchantWhenApprovalStatusChange(
    //     restaurantId: number,
    //     approvalStatus: ERestaurantApprovalStatus,
    //     provinceId: string,
    // ) {
    //     if (![ERestaurantApprovalStatus.APPROVED, ERestaurantApprovalStatus.DENIED].includes(approvalStatus)) return;

    //     const { deviceTokens, restaurant } = await this.getMerchantDeviceTokensOfRestaurant(restaurantId, provinceId);

    //     if (deviceTokens && deviceTokens.length > 0) {
    //         let title = '',
    //             message = '';
    //         if (approvalStatus == ERestaurantApprovalStatus.APPROVED) {
    //             title = `Cửa hàng đã xét duyệt thành công`;
    //             message = `Cửa hàng ${restaurant.name} đã được xét duyệt thành công!`;
    //         } else if (approvalStatus == ERestaurantApprovalStatus.DENIED) {
    //             title = `Cửa hàng đã xét duyệt thất bại`;
    //             message = `Cửa hàng ${restaurant.name} đã được xét duyệt thất bại!`;
    //         }
    //         this.firebaseAdmin.sendMessageFCM(deviceTokens, title, message, null, restaurant.image);
    //     }
    // }

    async getRestaurantInNewsfeedAdByNewsfeedId(newsfeedId: number, provinceId: string) {
        return await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
            .createQueryBuilder('restaurant')
            .innerJoinAndSelect(
                'restaurant.restaurantAds',
                'ads',
                'ads.is_active = 1 AND ((ads.active_from IS NULL AND ads.active_to IS NULL) OR (ads.active_from < current_timestamp() AND ads.active_to > current_timestamp()))',
            )
            .innerJoin('ads.newsFeedAds', 'nfAds', 'nfAds.news_feed_id = :newsfeedId', { newsfeedId })
            .orderBy({ ['nfAds.position']: 'ASC' })
            .getMany();
    }

    async getRestaurantByMerchant(provinceId: number, merchant_id: number) {
        const restaurants = await DatabaseService.getRepositoryByDefaultConnection(MerchantHasRole)
            .createQueryBuilder('r')
            // .innerJoinAndSelect('r.merchant', 'merchant')
            .where('r.province_id = :provinceId', { provinceId })
            .andWhere('r.merchant_id = :merchant_id', { merchant_id })
            .getMany();
        const ids = restaurants.map((e) => e.restaurant_id);
        if (ids && ids.length > 0) {
            return await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId.toString())
                .createQueryBuilder('restaurant')
                .where('restaurant.id IN (:...ids)', { ids })
                .getMany();
        }
        return [];
    }

    async updateBranch(restaurant_id: number, provinceId: string, branch_id?: number) {
        const restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
            where: { id: restaurant_id },
        });
        if (!restaurant) throw new BadRequestException('Không tìm thấy nhà hàng');
        const branchId = branch_id ? branch_id : null;

        const updateResult = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).update(
            { id: restaurant_id },
            { branch_id: branchId },
        );
        if (updateResult.affected) {
            const result = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
                where: { id: restaurant_id },
                relations: ['branch'],
            });
            return result.branch;
        }
    }

    async updateBranchForRestaurants(restaurant_ids: number[], provinceId: string, branch_id: number) {
        if (restaurant_ids.length > 0) {
            return await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
                .createQueryBuilder()
                .update()
                .set({ branch_id: branch_id })
                .where('id IN (:...restaurant_ids)', { restaurant_ids })
                .execute();
        }
    }

    async deleteBranchForRestaurants(restaurant_ids: number[], provinceId: string, branch_id: number) {
        if (restaurant_ids.length > 0) {
            return await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
                .createQueryBuilder()
                .update()
                .set({ branch_id: null })
                .where('id IN (:...restaurant_ids)', { restaurant_ids })
                .andWhere('branch_id = :branch_id', { branch_id })
                .execute();
        }
    }

    async getListWithRevenue({ created_month, limit, page }: GetRestaurantListWithRevenueDto, provinceId: string) {
        const builder = DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).createQueryBuilder(
            'restaurant',
        );
        const previousMonth = moment(created_month, 'YYYY-MM').subtract(1, 'months').format('YYYY-MM');
        const createdFrom = moment(created_month, 'YYYY-MM').startOf('month').format('YYYY-MM-DD');
        const createdTo = moment(created_month, 'YYYY-MM').endOf('month').format('YYYY-MM-DD');
        const previousMonthFrom = moment(previousMonth, 'YYYY-MM').startOf('month').format('YYYY-MM-DD');
        const previousMonthTo = moment(previousMonth, 'YYYY-MM').endOf('month').format('YYYY-MM-DD');
        const createdToVNTZ = moment(createdTo)
            .endOf('day')
            .utcOffset(VietNamTimeZoneNum)
            .format('YYYY-MM-DD HH:mm:ss');
        const previousMonthFromVNTZ = moment(previousMonthFrom)
            .startOf('day')
            .utcOffset(VietNamTimeZoneNum)
            .format('YYYY-MM-DD HH:mm:ss');
        const selects = [
            'restaurant.name AS name',
            'restaurant.code AS code',
            'restaurant.seller_id AS seller_id',
            'restaurant.created_at AS created_at',
            `SUM(IF(orders.order_date BETWEEN '${createdFrom}' AND '${createdTo}' AND orders.order_status_id = 5, orders.sub_total_price, 0)) AS created_month_revenue`,
            `SUM(IF(orders.order_date BETWEEN '${previousMonthFrom}' AND '${previousMonthTo}' AND orders.order_status_id = 5, orders.sub_total_price, 0)) AS previous_month_revenue`,
            'restaurant.author AS author',
        ];

        const result = await builder
            .select(selects)
            .leftJoin('restaurant.orders', 'orders')
            .where('restaurant.deleted_at IS NULL')
            .andWhere('restaurant.created_at BETWEEN :previousMonthFrom AND :createdTo', {
                createdTo: createdToVNTZ,
                previousMonthFrom: previousMonthFromVNTZ,
            })
            .groupBy('restaurant.id')
            .orderBy('restaurant.id', 'DESC')
            .offset((page - 1) * limit)
            .limit(limit)
            .getRawMany();

        const sellerIds = result.map((e) => e.seller_id);

        const sellers = await this.centralizedUserService.findByIds(sellerIds, provinceId);
        result.forEach((e) => {
            e.seller = sellers.find((s) => s.id == e.seller_id);
        });

        return result;
    }

    // getListByCreatedAt
    async getListWithRevenueV2({ created_month, limit, page }: GetRestaurantListWithRevenueDto, provinceId: string) {
        const builder = DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).createQueryBuilder(
            'restaurant',
        );
        const previousMonth = moment(created_month, 'YYYY-MM').subtract(1, 'months').format('YYYY-MM');
        const createdFrom = moment(created_month, 'YYYY-MM').startOf('month').format('YYYY-MM-DD');
        const createdTo = moment(created_month, 'YYYY-MM').endOf('month').format('YYYY-MM-DD');
        const previousMonthFrom = moment(previousMonth, 'YYYY-MM').startOf('month').format('YYYY-MM-DD');
        const previousMonthTo = moment(previousMonth, 'YYYY-MM').endOf('month').format('YYYY-MM-DD');
        const createdToVNTZ = moment(createdTo)
            .endOf('day')
            .utcOffset(VietNamTimeZoneNum)
            .format('YYYY-MM-DD HH:mm:ss');
        const previousMonthFromVNTZ = moment(previousMonthFrom)
            .startOf('day')
            .utcOffset(VietNamTimeZoneNum)
            .format('YYYY-MM-DD HH:mm:ss');
        const restaurants = await lastValueFrom(
            this.globalRestaurantService.getListByCreatedAt(
                createdToVNTZ,
                previousMonthFromVNTZ,
                page,
                limit,
                Number(provinceId),
            ),
        );
        const restaurant_ids = restaurants.map((e) => e.restaurant_id);
        let result = [];
        if (restaurant_ids && restaurant_ids.length > 0) {
            const selects = [
                'restaurant.name AS name',
                'restaurant.code AS code',
                'restaurant.id AS id',
                `SUM(IF(orders.order_date BETWEEN '${createdFrom}' AND '${createdTo}' AND orders.order_status_id = 5, orders.sub_total_price, 0)) AS created_month_revenue`,
                `SUM(IF(orders.order_date BETWEEN '${previousMonthFrom}' AND '${previousMonthTo}' AND orders.order_status_id = 5, orders.sub_total_price, 0)) AS previous_month_revenue`,
                'restaurant.author AS author',
            ];

            result = await builder
                .select(selects)
                .leftJoin('restaurant.orders', 'orders')
                .where('restaurant.deleted_at IS NULL')
                .andWhere(`restaurant.id IN(${restaurant_ids})`)
                .andWhere('restaurant.created_at BETWEEN :previousMonthFrom AND :createdTo', {
                    createdTo: createdToVNTZ,
                    previousMonthFrom: previousMonthFromVNTZ,
                })
                .groupBy('restaurant.id')
                .orderBy('restaurant.id', 'DESC')
                .getRawMany();
        }
        restaurants.forEach((e) => {
            const data = result.find((el) => el.id == e.restaurant_id);
            if (data) {
                e.author = data.author;
                e.previous_month_revenue = data.previous_month_revenue;
                e.created_month_revenue = data.created_month_revenue;
            } else {
                e.author = null;
                e.previous_month_revenue = 0;
                e.created_month_revenue = 0;
            }
            return e;
        });
        return restaurants;
    }

    async getRestaurantsByProvince(
        page: number,
        provinceId: number,
        restaurantId: number,
        search?: string,
        ids?: string,
    ) {
        const builder = DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).createQueryBuilder(
            'restaurant',
        );
        builder.where('restaurant.province_id = :provinceId', { provinceId });
        if (search) {
            builder.andWhere(
                '(restaurant.name LIKE :searchItem OR restaurant.code LIKE :searchItem OR restaurant.phone LIKE :searchItem)',
                {
                    searchItem: `%${search}%`,
                },
            );
        }
        if (ids) {
            const idArray = ids.split(',');
            builder.andWhere('restaurant.id IN (:...ids)', { ids: idArray.map((e) => Number(e)) });
        }

        const result = await builder
            .orderBy('restaurant.id', 'DESC')
            .offset((page - 1) * 20)
            .limit(20)
            .getManyAndCount();

        if (restaurantId) {
            const restaurant = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId).findOne({
                where: {
                    id: restaurantId,
                },
            });

            if (restaurant) {
                result[0] = result[0].filter((e) => e.id != restaurant.id);
                result[0].unshift(restaurant);
            }
        }

        return {
            items: result[0],
            total: result[1],
        };
    }

    async mapRestaurantPromotion(data: RestaurantPromotionV2[]) {
        const provinceIds = _.uniq(data.map((e) => e.province_id));

        const result = [];
        for (let i = 0; i < provinceIds.length; i++) {
            const provinceId = provinceIds[i];
            const restaurantIds = data.filter((e) => e.province_id == provinceId).map((e) => e.restaurant_id);
            const restaurants = await DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
                .createQueryBuilder('restaurant')
                .where('restaurant.id IN (:...restaurantIds)', { restaurantIds })
                .getMany();

            if (!restaurants || restaurants.length === 0) {
                continue;
            }

            for (let j = 0; j < data.length; j++) {
                const item = data[j];
                const restaurant = restaurants.find(
                    (e) => e.id == item.restaurant_id && item.province_id == e.province_id,
                );
                if (restaurant) {
                    result.push({
                        ...item,
                        restaurant,
                    });
                }
            }
        }

        return result;
    }

    async getRestaurantsHaveNoRevenue(provinceId: string, params: GetExportRestaurantDto) {
        const { from_date, to_date, page, limit } = params;
        if (!from_date || !to_date) {
            throw new BadRequestException('Khoảng thời gian không hợp lệ!');
        }
        const offset = page * limit;
        const qb = DatabaseService.getRepositoryByProvinceId(Order, provinceId)
            .createQueryBuilder('order')
            .select('DISTINCT order.restaurant_id', 'id')
            .where(
                `(order.order_date BETWEEN '${from_date.format('YYYY-MM-DD')}' AND '${to_date.format('YYYY-MM-DD')}')`,
            )
            .andWhere(`order.order_status_id = 5`)
            .andWhere('order.restaurant_id is not null')
            .andWhere(`order.type = '${OrderType.VILLFOOD}'`)
            .getQuery();
        const builder = DatabaseService.getRepositoryByProvinceId(Restaurant, provinceId)
            .createQueryBuilder('restaurants')
            .addCommonTableExpression(qb, 'temp_restaurant_ids')
            .leftJoinAndSelect('restaurants.province', 'province');
        builder.where(`restaurants.id NOT IN ( SELECT temp_restaurant_ids.id from temp_restaurant_ids)`);
        builder
            .andWhere(
                `(restaurants.operating_status = '${ERestaurantOperatingStatus.OPEN}' OR restaurants.approval_status = '${ERestaurantOperatingStatus.TEMPT_CLOSE}')`,
            )
            .andWhere(`restaurants.created_at <= '${to_date.format('YYYY-MM-DD')}'`);
        return await builder.orderBy('restaurants.id', 'ASC').limit(limit).offset(offset).getMany();
    }

    async deleteMerchantInRestaurant(merchantId: number, restaurantId: number, provinceId: string) {
        this.eventService.deleteRestaurantMerchantEvent(merchantId, restaurantId, +provinceId);
        return await lastValueFrom(
            this.merchantHasRoleService.deleteMerchantFromRestaurant(merchantId, restaurantId, Number(provinceId)),
        );
    }

    async updateMerchantRoleInRestaurant(
        restaurantId: number,
        merchantId: number,
        provinceId: string,
        body: UpdateMerchantDto,
    ) {
        const restaurant = await this.getRestaurantByIdWithFullDetails(restaurantId, provinceId);
        const updateGlobalData = new UpdateDataDto();
        Object.assign(updateGlobalData, restaurant);
        updateGlobalData.restaurant_id = restaurantId;
        return await lastValueFrom(
            this.merchantHasRoleService.updateMerchant(
                merchantId,
                body.roleId,
                body.isActive,
                updateGlobalData,
                Number(provinceId),
            ),
        );
    }

    async addMerchantToRestaurant(restaurantId: number, merchantId: number, provinceId: string) {
        const restaurant = await this.getRestaurantByIdWithFullDetails(restaurantId, provinceId);
        const updateGlobalData = new UpdateDataDto();
        Object.assign(updateGlobalData, restaurant);
        updateGlobalData.restaurant_id = restaurantId;
        return await lastValueFrom(
            this.merchantHasRoleService.addMerchantToRestaurant(merchantId, updateGlobalData, Number(provinceId)),
        );
    }
}
