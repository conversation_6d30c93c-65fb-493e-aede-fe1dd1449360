import { Transform } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsDateString,
    IsEnum,
    IsIn,
    IsLatitude,
    IsLongitude,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    Min,
    ValidateIf,
} from 'class-validator';
import * as _ from 'lodash';
import { TinyInt } from 'src/common/constants';
import {
    ERestaurantApprovalStatus,
    ERestaurantOperatingStatus,
    RestaurantTradeDiscountPeriodType,
} from 'src/entities/restaurant.entity';
import { CreateRestaurantLocationWithoutRestaurantIdDto } from './create-restaurant-location.dto';

export class UpdateBusinessHourDto {
    day: number;
    is_active: number;
    id: number;
    workTimes: [
        {
            id: number;
            open_time: string;
            close_time: string;
        },
    ];
}

export class UpdateRestaurantDto {
    @IsNotEmpty()
    @IsString()
    name: string;

    @IsNotEmpty()
    @IsString()
    address: string;

    @IsOptional()
    location: CreateRestaurantLocationWithoutRestaurantIdDto;

    /* @IsOptional()
    @IsArray()
    users: Array<number>; */

    @IsNotEmpty()
    @IsArray()
    categories: Array<number>;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    deliveryFee: number;

    @IsNotEmpty()
    @IsString()
    phone: string;

    @IsNotEmpty()
    @IsString()
    mobile: string;

    @IsNotEmpty()
    @IsLatitude()
    latitude: string;

    @IsNotEmpty()
    @IsLongitude()
    longitude: string;

    /*  @IsNotEmpty()
    @IsString()
    openTime: string;

    @IsNotEmpty()
    @IsString()
    closeTime: string; */

    @ValidateIf(({ value }) => value != null && value !== undefined)
    @IsDateString()
    reopenTime: string;

    @IsOptional()
    @IsString()
    description: string;

    @IsOptional()
    @IsString()
    information: string;

    @IsNotEmpty()
    @IsBoolean()
    star: boolean;

    @IsNotEmpty()
    @IsBoolean()
    freeship: boolean;

    @IsNotEmpty()
    @IsBoolean()
    top: boolean;

    @IsNotEmpty()
    @IsBoolean()
    mondoc: boolean;

    @IsNotEmpty()
    @IsBoolean()
    buoisang: boolean;

    @IsNotEmpty()
    @IsBoolean()
    buoitoi: boolean;

    @IsNotEmpty()
    @IsBoolean()
    buoitrua: boolean;

    @IsNotEmpty()
    @IsBoolean()
    cooperating: boolean;

    // @IsNotEmpty()
    // @IsBoolean()
    // onTop: boolean;

    @IsNotEmpty()
    @IsBoolean()
    status: boolean;

    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsIn([0, 1])
    @IsNumber()
    top_trending: number;

    @IsNotEmpty()
    @IsEnum(ERestaurantOperatingStatus)
    operatingStatus: ERestaurantOperatingStatus;

    @IsNotEmpty()
    businessHours: UpdateBusinessHourDto[];

    @IsNotEmpty()
    @IsString()
    code: string;

    @IsOptional()
    @IsNumber()
    frameId: number;

    @IsNotEmpty()
    @IsEnum(ERestaurantApprovalStatus)
    approval_status: ERestaurantApprovalStatus;

    @IsOptional()
    @IsIn([TinyInt.NO, TinyInt.YES])
    long_preparing: TinyInt;

    @IsOptional()
    @IsIn([TinyInt.NO, TinyInt.YES])
    mid_afternoon: TinyInt;

    @IsOptional()
    @IsIn([TinyInt.NO, TinyInt.YES])
    newsfeed_ads: TinyInt;

    @IsOptional()
    @IsNumber()
    provinceId: number;

    @IsOptional()
    @IsBoolean()
    is_trial: boolean;

    @IsOptional()
    @IsNumber()
    branchId: number;

    @IsOptional()
    @IsNumber()
    ad_promo: number;

    @IsOptional()
    @IsBoolean()
    on_top: boolean;

    @IsOptional()
    @IsString()
    address_note: string;
}

export class UpdateRestaurantAdminSettingDto {
    @IsNotEmpty()
    @IsIn([0, 1])
    tradeDiscountType: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    tradeDiscount = 0;

    @IsNotEmpty()
    @IsEnum(RestaurantTradeDiscountPeriodType)
    tradeDiscountPeriodType: RestaurantTradeDiscountPeriodType;

    @IsNotEmpty()
    @IsNumber()
    @IsIn([TinyInt.NO, TinyInt.YES])
    ad_promo: TinyInt;

    @IsNotEmpty()
    @IsNumber()
    @IsIn([TinyInt.NO, TinyInt.YES])
    roundTradeDiscount: TinyInt;

    // @IsNotEmpty()
    // @IsBoolean()
    // onTop: boolean;
}

export class UpdateRestaurantOnTopDto {
    @IsNotEmpty()
    @IsBoolean()
    onTop: boolean;
}
