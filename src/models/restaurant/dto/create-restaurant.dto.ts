import { Transform } from 'class-transformer';
import {
    IsArray,
    IsBoolean,
    IsDate,
    IsEnum,
    IsIn,
    IsLatitude,
    IsLongitude,
    IsNotEmpty,
    IsNumber,
    IsOptional,
    IsString,
    Min,
} from 'class-validator';
import * as _ from 'lodash';
import { TinyInt } from 'src/common/constants';
import {
    ERestaurantApprovalStatus,
    ERestaurantOperatingStatus,
    RestaurantTradeDiscountPeriodType,
} from 'src/entities/restaurant.entity';
import { CreateRestaurantLocationWithoutRestaurantIdDto } from './create-restaurant-location.dto';

export class CreateBusinessHourDto {
    day: number;
    is_active: number;
    workTimes: [
        {
            open_time: string;
            close_time: string;
        },
    ];
}

export class CreateRestaurantDto {
    @IsNotEmpty()
    @IsString()
    name: string;

    @IsNotEmpty()
    @IsString()
    address: string;

    // @IsNotEmpty()
    @IsOptional()
    location: CreateRestaurantLocationWithoutRestaurantIdDto;

    /* @IsOptional()
    @IsArray()
    users: Array<number>; */

    @IsNotEmpty()
    @IsArray()
    categories: Array<number>;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    deliveryFee: number;

    @IsNotEmpty()
    @IsString()
    phone: string;

    @IsNotEmpty()
    @IsString()
    mobile: string;

    @IsNotEmpty()
    @IsLatitude()
    latitude: string;

    @IsNotEmpty()
    @IsLongitude()
    longitude: string;

    /* @IsNotEmpty()
    @IsString()
    openTime: string;

    @IsNotEmpty()
    @IsString()
    closeTime: string; */

    @IsOptional()
    @IsString()
    description = '';

    @IsOptional()
    @IsString()
    information = '';

    @IsNotEmpty()
    @IsIn([0, 1])
    tradeDiscountType: number;

    @IsNotEmpty()
    @IsNumber()
    @Min(0)
    tradeDiscount = 0;

    @IsNotEmpty()
    @IsEnum(RestaurantTradeDiscountPeriodType)
    tradeDiscountPeriodType: RestaurantTradeDiscountPeriodType;

    @IsNotEmpty()
    @IsBoolean()
    star: boolean;

    @IsNotEmpty()
    @IsBoolean()
    freeship: boolean;

    @IsNotEmpty()
    @IsBoolean()
    top: boolean;

    @IsNotEmpty()
    @IsBoolean()
    mondoc: boolean;

    @IsNotEmpty()
    @IsBoolean()
    buoisang: boolean;

    @IsNotEmpty()
    @IsBoolean()
    buoitoi: boolean;

    @IsNotEmpty()
    @IsBoolean()
    buoitrua: boolean;

    @IsNotEmpty()
    @IsBoolean()
    cooperating: boolean;

    @IsNotEmpty()
    @IsBoolean()
    onTop: boolean;

    @IsNotEmpty()
    @IsIn([null, 'fixed'])
    couponType: string;

    @IsOptional()
    @IsString()
    coupon: string;

    @IsOptional()
    @IsNumber()
    @Min(0)
    couponOrder: number;

    @IsOptional()
    @IsNumber()
    @Min(0)
    couponValue: number;

    @IsOptional()
    @IsDate()
    couponExp: Date;

    @IsOptional()
    @IsNumber()
    @Min(0)
    couponTime: number;

    @IsNotEmpty()
    @IsBoolean()
    status: boolean;

    @IsNotEmpty()
    @Transform(({ value }) => _.toNumber(value))
    @IsIn([0, 1])
    @IsNumber()
    top_trending: number;

    @IsNotEmpty()
    @IsEnum(ERestaurantOperatingStatus)
    operatingStatus: ERestaurantOperatingStatus;

    @IsNotEmpty()
    businessHours: CreateBusinessHourDto[];

    @IsNotEmpty()
    @IsString()
    code: string;

    @IsNotEmpty()
    @IsEnum(ERestaurantApprovalStatus)
    approval_status: ERestaurantApprovalStatus;

    @IsOptional()
    @IsIn([TinyInt.NO, TinyInt.YES])
    long_preparing: TinyInt;

    @IsNotEmpty()
    restaurant_province_id: string;

    @IsNotEmpty()
    @IsBoolean()
    is_trial: boolean;

    @IsOptional()
    @IsArray()
    @IsNumber({}, { each: true })
    paymentMethods: Array<number> = [];

    @IsOptional()
    @IsNumber()
    branchId: number;

    @IsOptional()
    @IsString()
    address_note: string;
}
