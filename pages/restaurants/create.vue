<template>
    <a-layout-content>
        <a-page-header title="TẠO CỬA HÀNG MỚI" @back="() => $router.go(-1)">
            <Breadcrumb
                :routes="[
                    {
                        path: '/restaurants',
                        breadcrumbName: 'DANH SÁCH CỬA HÀNG',
                    },
                    {
                        path: '',
                        breadcrumbName: 'TẠO CỬA HÀNG',
                    },
                ]"
            />
        </a-page-header>
        <div class="content-card">
            <a-spin :spinning="createRestaurantPending">
                <a-form-model
                    ref="createRestaurantRef"
                    layout="vertical"
                    :label-col="{ span: 6 }"
                    :wrapper-col="{ span: 24 }"
                    :rules="rules"
                    :model="form"
                >
                    <a-row :gutter="24">
                        <a-col :span="14">
                            <a-card title="Thông tin chung">
                                <a-form-model-item label="Tên cửa hàng" prop="name">
                                    <a-input size="large" v-model="form.name" placeHolder="Nhập tên cửa hàng" />
                                </a-form-model-item>
                                <a-form-model-item label="Thị trường" prop="name">
                                    <SubProvinceSelect
                                        :value="form.restaurant_province_id"
                                        @change="(value) => (form.restaurant_province_id = value)"
                                    />
                                </a-form-model-item>
                                <a-form-model-item label="Mã nhà hàng" prop="code">
                                    <a-input-search
                                        size="large"
                                        v-model="form.code"
                                        placeHolder="Nhập mã code nhà hàng"
                                        @search="generateCode(form.restaurant_province_id)"
                                        :disabled="!form.restaurant_province_id"
                                    >
                                        <a-button
                                            :disabled="generateCodePending || !form.restaurant_province_id"
                                            slot="enterButton"
                                            type="primary"
                                        >
                                            Tạo mã
                                        </a-button>
                                    </a-input-search>
                                    <p v-if="generateCodePending">Đang tạo mã...</p>
                                </a-form-model-item>
                                <!--  <a-form-model-item label="Chủ cửa hàng">
                                    <RestaurantOwnerFilter v-model="form.users" />
                                </a-form-model-item> -->
                                <a-form-model-item label="Loại" prop="categories">
                                    <CategoryTagFilter v-model="form.categories" />
                                </a-form-model-item>
                                <a-form-model-item label="* Mô tả">
                                    <!--   <div>
                                        <a-button
                                            size="small"
                                            type="dashed"
                                            icon="redo"
                                            style="margin-bottom: 4px"
                                            @click="handleGenerateDescription"
                                            :loading="descriptionPending"
                                        >
                                            Tạo tự động
                                        </a-button> -->
                                    <RichEditor v-model="form.description" />
                                    <!-- </div> -->
                                </a-form-model-item>
                                <a-form-model-item label="Thông tin">
                                    <RichEditor v-model="form.information" />
                                </a-form-model-item>
                                <!-- <a-form-model-item label="Mở cửa" prop="openTime">
                                    <a-input size="large" v-model="form.openTime" />
                                </a-form-model-item>
                                <a-form-model-item label="Đóng cửa" prop="closeTime">
                                    <a-input size="large" v-model="form.closeTime" />
                                </a-form-model-item> -->
                            </a-card>
                            <br />
                            <a-card title="Liên lạc">
                                <a-form-model-item label="Phone" prop="phone">
                                    <a-input size="large" v-model="form.phone" />
                                </a-form-model-item>
                                <a-form-model-item label="Mobile" prop="mobile">
                                    <a-input size="large" v-model="form.mobile" />
                                </a-form-model-item>

                                <a-form-model-item label="Latitude" prop="latitude">
                                    <a-input size="large" v-model="form.latitude" />
                                </a-form-model-item>
                                <a-form-model-item label="Longitude" prop="longitude">
                                    <a-input size="large" v-model="form.longitude" />
                                </a-form-model-item>
                                <a-row>
                                    <a-form-model-item prop="cityId" label="Tỉnh/Thành Phố">
                                        <a-select
                                            size="large"
                                            v-model="form.cityId"
                                            @change="onChangeCity"
                                            show-search
                                            :filter-option="false"
                                            @search="(q) => filterOptionLocation(q, location.ELocation.CITY)"
                                            placeholder="Chọn Tỉnh/Thành Phố"
                                        >
                                            <a-select-option
                                                v-for="city in filteredCities"
                                                :key="city.id"
                                                :value="city.id"
                                                >{{ city.name }}</a-select-option
                                            >
                                        </a-select>
                                    </a-form-model-item>
                                </a-row>
                                <a-row>
                                    <a-form-model-item prop="districtId" label="Quận / Huyện">
                                        <a-select
                                            size="large"
                                            v-model="form.districtId"
                                            @change="onChangeDistrict"
                                            show-search
                                            :filter-option="false"
                                            @search="(q) => filterOptionLocation(q, location.ELocation.DISTRICT)"
                                        >
                                            <a-select-option
                                                v-for="district in filteredDistricts"
                                                :key="district.id"
                                                :value="district.id"
                                                >{{ district.name }}</a-select-option
                                            >
                                        </a-select>
                                    </a-form-model-item>
                                </a-row>

                                <a-row>
                                    <a-form-model-item prop="locationId" label="Phường/Xã">
                                        <a-select
                                            size="large"
                                            v-model="form.locationId"
                                            show-search
                                            :filter-option="false"
                                            @change="changeAddress"
                                            @search="(q) => filterOptionLocation(q, location.ELocation.WARD)"
                                        >
                                            <a-select-option
                                                v-for="ward in filteredWards"
                                                :key="ward.id"
                                                :value="ward.id"
                                                >{{ ward.name }}</a-select-option
                                            >
                                        </a-select>
                                    </a-form-model-item>
                                </a-row>
                                <a-form-model-item label="Địa chỉ cụ thể" prop="street">
                                    <a-input size="large" v-model="form.street" @change="changeAddress" />
                                </a-form-model-item>
                                <a-form-model-item label="Địa chỉ (ghi chú) " prop="address_note">
                                    <a-input size="large" v-model="form.address_note" />
                                </a-form-model-item>
                                <a-form-model-item label="Địa chỉ" prop="address">
                                    <a-input size="large" v-model="form.address" />
                                </a-form-model-item>
                            </a-card>
                            <br />
                            <a-card title="Giờ hoạt động">
                                <template slot="extra">
                                    <a-space>
                                        <span>Thời gian mặc định</span>
                                        <a-time-picker v-model="defaultBusinessHours.openTime" format="HH:mm" />
                                        <span>-</span>
                                        <a-time-picker v-model="defaultBusinessHours.closeTime" format="HH:mm" />
                                        <a-button type="primary" @click="setDefaultBusinessHours">Áp dụng</a-button>
                                    </a-space>
                                </template>
                                <a-row :gutter="[12, 12]" v-for="item in form.businessHours" :key="item.day">
                                    <a-col :span="4">
                                        {{ parseDayOfWeek(item.day) }}
                                    </a-col>
                                    <a-col :span="4">
                                        <a-switch
                                            :checked="!!item.is_active"
                                            @change="
                                                (checked) =>
                                                    onDayBusinessHourStatusChange(item.day, checked == true ? 1 : 0)
                                            "
                                        >
                                        </a-switch>
                                    </a-col>
                                    <a-col :span="14">
                                        <a-row>
                                            <a-col :span="20" v-if="item.workTimes.length > 0">
                                                <a-row
                                                    :gutter="[12, 12]"
                                                    v-for="(workTime, idx) in item.workTimes"
                                                    :key="idx"
                                                >
                                                    <a-col :span="22">
                                                        <a-input-group>
                                                            <a-row :gutter="[12, 12]">
                                                                <a-col :span="11">
                                                                    <a-time-picker
                                                                        :disabled="!item.is_active"
                                                                        v-model="workTime.open_time"
                                                                        style="width: 100%"
                                                                        format="HH:mm"
                                                                    />
                                                                </a-col>
                                                                <a-col :span="2">
                                                                    <a-row type="flex" justify="center"> - </a-row>
                                                                </a-col>
                                                                <a-col :span="11">
                                                                    <a-time-picker
                                                                        :disabled="!item.is_active"
                                                                        v-model="workTime.close_time"
                                                                        style="width: 100%"
                                                                        format="HH:mm"
                                                                    />
                                                                </a-col>
                                                            </a-row>
                                                        </a-input-group>
                                                    </a-col>
                                                    <a-col :span="2">
                                                        <a-button
                                                            v-if="item.workTimes.length > 0"
                                                            type="link"
                                                            icon="close"
                                                            @click="removeWorkTimes(item.day, idx)"
                                                        >
                                                        </a-button>
                                                    </a-col>
                                                </a-row>
                                            </a-col>
                                            <a-col :span="20" v-else>Hoạt động 24h</a-col>
                                            <a-col :span="4">
                                                <a-button type="link" @click="addWorkTimes(item.day)"
                                                    >Thêm khung giờ
                                                </a-button>
                                            </a-col>
                                        </a-row>
                                    </a-col>
                                </a-row>
                            </a-card>
                        </a-col>
                        <a-col :span="10">
                            <a-card v-if="!fetchListPending" title="Chiết khấu">
                                <a-form-model-item label="Chu kỳ đóng chiết khấu" prop="tradeDiscountPeriodType">
                                    <a-select size="large" v-model="form.tradeDiscountPeriodType">
                                        <a-select-option value="direct">Thu thực tiếp</a-select-option>
                                        <a-select-option value="week">Thu theo tuần</a-select-option>
                                        <a-select-option value="month">Thu theo tháng</a-select-option>
                                        <a-select-option value="dr_wallet">
                                            Trừ trực tiếp vào ví merchant
                                        </a-select-option>
                                    </a-select>
                                </a-form-model-item>
                                <a-form-model-item label="Loại chiết khấu" prop="tradeDiscountType">
                                    <a-select size="large" v-model="form.tradeDiscountType">
                                        <a-select-option :value="0">Chiết khấu theo phần trăm</a-select-option>
                                        <a-select-option :value="1">Chiết khấu theo giá tiền</a-select-option>
                                    </a-select>
                                </a-form-model-item>
                                <a-form-model-item label="Chiết khấu" prop="tradeDiscount">
                                    <a-input-number
                                        style="width: 100%"
                                        size="large"
                                        :min="0"
                                        :value="form.tradeDiscount"
                                        disabled
                                    />
                                    <div slot="help">
                                        <div v-if="form.tradeDiscountType === 0">
                                            Chiết khấu theo % = {{ form.tradeDiscount }}%
                                        </div>
                                        <div v-else>Chiết khấu theo giá tiền {{ form.tradeDiscount + '.000đ' }}</div>
                                    </div>
                                </a-form-model-item>
                            </a-card>
                            <a-card v-else title="Chiết khấu">
                                <a-spin />
                            </a-card>
                            <br />
                            <a-card v-if="!fetchPaymentMethodListPending" title="Phương thức thanh toán">
                                <a-form-model-item label="Phương thức thanh toán">
                                    <a-select size="large" v-model="paymentType">
                                        <a-select-option :value="0">Tất cả</a-select-option>
                                        <a-select-option :value="1">Chỉ định</a-select-option>
                                    </a-select>
                                </a-form-model-item>
                                <a-form-model-item
                                    style="position: relative"
                                    v-if="paymentType === 1"
                                    label="Loại thanh toán"
                                >
                                    <div style="position: absolute; top: -40px; right: -12px">
                                        <a-button
                                            type="link"
                                            @click="
                                                () =>
                                                    (paymentMethods = paymentMethodList.map(
                                                        (paymentMethod) => paymentMethod.id,
                                                    ))
                                            "
                                        >
                                            Chọn tất cả
                                        </a-button>
                                    </div>
                                    <a-select size="large" v-model="paymentMethods" mode="multiple" allowClear required>
                                        <a-select-option
                                            v-for="paymentMethod in paymentMethodList"
                                            :key="paymentMethod.id"
                                            :value="paymentMethod.id"
                                        >
                                            {{ paymentMethod.name }}
                                        </a-select-option>
                                    </a-select>
                                </a-form-model-item>
                            </a-card>
                            <a-card v-else title="Phương thức thanh toán">
                                <a-spin />
                            </a-card>
                            <br />
                            <a-card title="Thiết lập">
                                <a-form-model-item label="Trạng thái" prop="operatingStatus">
                                    <a-select size="large" v-model="form.operatingStatus">
                                        <a-select-option value="open">Đang mở cửa</a-select-option>
                                        <a-select-option value="tempt_close">Tạm thời đóng cửa</a-select-option>
                                        <a-select-option value="contact_terminated">Ngừng hợp tác</a-select-option>
                                    </a-select>
                                </a-form-model-item>
                                <a-form-model-item label="Trạng thái xét duyệt" prop="approval_status">
                                    <a-select size="large" v-model="form.approval_status">
                                        <!-- <a-select-option value="approved">Đã duyệt</a-select-option> -->
                                        <a-select-option value="pending">Đang chờ</a-select-option>
                                        <!-- <a-select-option value="denied">Từ chối</a-select-option> -->
                                    </a-select>
                                </a-form-model-item>
                                <a-form-model-item label="Quán thử nghiệm">
                                    <a-switch v-model="form.is_trial" />
                                </a-form-model-item>
                                <a-form-model-item label="Nhà hàng FREESHIP">
                                    <a-switch v-model="form.freeship" />
                                </a-form-model-item>
                                <a-form-model-item label="Nhà hàng top khuyến mãi">
                                    <a-switch v-model="form.top" />
                                </a-form-model-item>
                                <a-form-model-item label="Món độc quán quen">
                                    <a-switch v-model="form.mondoc" />
                                </a-form-model-item>
                                <a-form-model-item label="Buổi sáng ăn gì?">
                                    <a-switch v-model="form.buoisang" />
                                </a-form-model-item>
                                <a-form-model-item label="Buổi trưa">
                                    <a-switch v-model="form.buoitrua" />
                                </a-form-model-item>
                                <a-form-model-item label="Buổi tối">
                                    <a-switch v-model="form.buoitoi" />
                                </a-form-model-item>
                                <a-form-model-item label="On top">
                                    <a-switch v-model="form.onTop" />
                                </a-form-model-item>
                                <a-form-model-item label="Quán đối tác">
                                    <a-switch v-model="form.star" />
                                </a-form-model-item>
                                <a-form-model-item label="Đang hợp tác">
                                    <a-switch v-model="form.cooperating" />
                                </a-form-model-item>
                                <a-form-model-item label="Quán đang mở cửa">
                                    <a-switch v-model="form.status" />
                                </a-form-model-item>
                                <a-form-model-item label="Nằm trong danh sách xu hướng">
                                    <a-switch v-model="form.top_trending" />
                                </a-form-model-item>
                                <a-form-model-item label="Có món ăn buổi xế chiều">
                                    <a-switch v-model="form.mid_afternoon" />
                                </a-form-model-item>
                            </a-card>
                            <br />
                            <a-card title="Thiết lập khác">
                                <a-form-model-item label="Thuộc branch" prop="branchId">
                                    <BranchSelector
                                        :value="form.branchId"
                                        @change="(value) => (form.branchId = value)"
                                    />
                                </a-form-model-item>
                            </a-card>
                        </a-col>
                    </a-row>
                    <br />
                    <a-row type="flex" justify="end">
                        <a-button :disabled="createRestaurantPending" type="primary" size="large" @click="onSubmit"
                            >Tạo mới</a-button
                        >
                    </a-row>
                </a-form-model>
            </a-spin>
        </div>
    </a-layout-content>
</template>

<script>
import { mapState, mapActions } from 'vuex';
import * as moment from 'moment';

import RestaurantOwnerFilter from '@/components/common/RestaurantOwnerFilter.vue';
import CategoryTagFilter from '@/components/common/CategoryTagFilter.vue';
import ShipperTagFilter from '@/components/common/RestaurantShipperTagFilter.vue';
import ImageUploader from '@/components/common/ImageUploader.vue';
import Breadcrumb from '@/components/common/Breadcrumb';
import RichEditor from '@/components/common/Edittor/index.vue';
import { fetchLocationListByParentId } from '@/api/location.api';
import * as restaurantHelper from '@/utils/restaurant.helper';
import { removeUnicode, getProvinceIdStorage } from '@/utils/common';
import SubProvinceSelect from '@/components/province/subProvinceSelect.vue';
import BranchSelector from '@/components/branch/branchSelector.vue';
export default {
    name: 'RestaurantEditingPage',
    components: {
        RestaurantOwnerFilter,
        CategoryTagFilter,
        ShipperTagFilter,
        ImageUploader,
        Breadcrumb,
        RichEditor,
        SubProvinceSelect,
        BranchSelector,
    },
    computed: {
        ...mapState('restaurantCounter', {
            code: 'code',
            generateCodePending: 'generateCodePending',
            generateCodeSuccess: 'generateCodeSuccess',
            generateCodeError: 'generateCodeError',
        }),
        ...mapState('restaurant', {
            restaurant: (state) => state.restaurant,
            createRestaurantPending: (state) => state.createRestaurantPending,
            createRestaurantSuccess: (state) => state.createRestaurantSuccess,
            createRestaurantError: (state) => state.createRestaurantError,
            createRestaurantMsg: (state) => state.createRestaurantMsg,

            description: (state) => state.description,
            descriptionPending: (state) => state.descriptionPending,
            descriptionSuccess: (state) => state.descriptionSuccess,
            descriptionError: (state) => state.descriptionError,
            descriptionMsg: (state) => state.descriptionMsg,
        }),
        ...mapState('location', {
            locationList: (state) => state.locationList,
            fetchLocationListPending: (state) => state.fetchLocationListPending,
            fetchLocationListSuccess: (state) => state.fetchLocationListSuccess,
            fetchLocationListError: (state) => state.fetchLocationListError,
            fetchLocationListMsg: (state) => state.fetchLocationListMsg,

            childLocationList: (state) => state.childLocationList,
            fetchLocationListByParentIdSuccess: (state) => state.fetchLocationListByParentIdSuccess,
            fetchLocationListByParentIdPending: (state) => state.fetchLocationListByParentIdPending,
            fetchLocationListByParentIdError: (state) => state.fetchLocationListByParentIdError,
            fetchLocationListByParentIdMsg: (state) => {
                state.fetchLocationListByParentIdMsg;
            },
        }),
        ...mapState('province', {
            fetchListProvincePending: (state) => state.fetchListPending,
            fetchListProvinceError: (state) => state.fetchListError,
            fetchListProvinceSuccess: (state) => state.fetchListSuccess,
            fetchListProvinceMsg: (state) => state.fetchListMsg,
            listProvince: (state) => state.list || [],
        }),
        ...mapState('appSetting', {
            RESTAURANT_TRADE_DISCOUNT_DEFAULT: 'RESTAURANT_TRADE_DISCOUNT_DEFAULT',
            fetchListPending: 'fetchListPending',
        }),
        ...mapState('paymentMethod', {
            paymentMethodList: (state) => state.list,
            fetchPaymentMethodListPending: (state) => state.fetchListPending,
            fetchPaymentMethodListSuccess: (state) => state.fetchListSuccess,
            fetchPaymentMethodListError: (state) => state.fetchListError,
        }),
        filteredCities() {
            const cities = JSON.parse(JSON.stringify(this.location.cities));
            return cities.filter((city) => removeUnicode(city.name.toLowerCase()).includes(this.location.queryCity));
        },
        filteredDistricts() {
            const districts = JSON.parse(JSON.stringify(this.location.districts));
            return districts.filter((district) =>
                removeUnicode(district.name.toLowerCase()).includes(this.location.queryDistrict),
            );
        },
        filteredWards() {
            const wards = JSON.parse(JSON.stringify(this.location.wards));
            return wards.filter((ward) => removeUnicode(ward.name.toLowerCase()).includes(this.location.queryWard));
        },
    },
    created() {
        this.fetchListSetting();
        this.fetchLocationList();
        this.fetchPaymentMethodList();
        this.fetchActiveProvinces();
    },

    data() {
        return {
            location: {
                districts: [],
                wards: [],
                cities: [],
                queryCity: '',
                queryDistrict: '',
                queryWard: '',
                ELocation: {
                    CITY: 'CITY',
                    DISTRICT: 'DISTRICT',
                    WARD: 'WARD',
                },
            },
            provinceId: getProvinceIdStorage(),
            defaultBusinessHours: {
                openTime: null,
                closeTime: null,
            },
            paymentMethods: [],
            paymentType: 0,
            form: {
                name: undefined,
                address: undefined,
                street: '',
                locationId: '',
                districtId: '',
                cityId: '',
                // users: [],
                categories: [],
                deliveryFee: 0,
                phone: undefined,
                mobile: undefined,
                latitude: undefined,
                longitude: undefined,
                // openTime: undefined,
                // closeTime: undefined,
                description: undefined,
                information: undefined,
                tradeDiscountType: 0,
                tradeDiscount: 0,
                tradeDiscountPeriodType: 'direct',
                star: false,
                freeship: false,
                is_trial: false,
                top: false,
                mondoc: false,
                buoisang: false,
                buoitoi: false,
                buoitrua: false,
                cooperating: false,
                onTop: false,
                couponType: 'fixed',
                coupon: null,
                couponOrder: null,
                couponValue: null,
                couponExp: null,
                status: false,
                couponTime: null,
                mid_afternoon: null,
                top_trending: false,
                address_note: '',
                businessHours: [
                    {
                        day: 1,
                        is_active: 1,
                        workTimes: [
                            {
                                open_time: moment('08:00', 'HH:mm'),
                                close_time: moment('17:00', 'HH:mm'),
                            },
                        ],
                    },
                    {
                        day: 2,
                        is_active: 1,
                        workTimes: [
                            {
                                open_time: moment('08:00', 'HH:mm'),
                                close_time: moment('17:00', 'HH:mm'),
                            },
                        ],
                    },
                    {
                        day: 3,
                        is_active: 1,
                        workTimes: [
                            {
                                open_time: moment('08:00', 'HH:mm'),
                                close_time: moment('17:00', 'HH:mm'),
                            },
                        ],
                    },
                    {
                        day: 4,
                        is_active: 1,
                        workTimes: [
                            {
                                open_time: moment('08:00', 'HH:mm'),
                                close_time: moment('17:00', 'HH:mm'),
                            },
                        ],
                    },
                    {
                        day: 5,
                        is_active: 1,
                        workTimes: [
                            {
                                open_time: moment('08:00', 'HH:mm'),
                                close_time: moment('17:00', 'HH:mm'),
                            },
                        ],
                    },
                    {
                        day: 6,
                        is_active: 1,
                        workTimes: [
                            {
                                open_time: moment('08:00', 'HH:mm'),
                                close_time: moment('17:00', 'HH:mm'),
                            },
                        ],
                    },
                    {
                        day: 7,
                        is_active: 1,
                        workTimes: [
                            {
                                open_time: moment('08:00', 'HH:mm'),
                                close_time: moment('17:00', 'HH:mm'),
                            },
                        ],
                    },
                ],
                operatingStatus: 'open',
                code: '',
                approval_status: 'pending',
                restaurant_province_id: getProvinceIdStorage(),
                branchId: undefined,
            },
            rules: {
                name: [
                    {
                        required: true,
                        message: 'Please input Restaurant name',
                        trigger: 'blur',
                    },
                ],
                // users: [
                //     { required: true, message: 'Please select Restaurant owners', trigger: 'change' }
                // ],
                categories: [
                    {
                        required: true,
                        message: 'Please select Restaurant categories',
                        trigger: 'blur',
                    },
                ],
                address: [{ required: true, message: 'Please input address', trigger: 'blur' }],
                // cityId: [
                //     { required: true, message: "Please input city", trigger: "blur" },
                // ],
                // districtId: [
                //     {
                //         required: true,
                //         message: "Please input district",
                //         trigger: "blur",
                //     },
                // ],
                // locationId: [
                //     { required: true, message: "Please input ward", trigger: "blur" },
                // ],
                // street: [
                //     { required: true, message: "Please input street", trigger: "blur" },
                // ],
                phone: [{ required: true, message: 'Please input phone', trigger: 'blur' }],
                mobile: [{ required: true, message: 'Please input mobile', trigger: 'blur' }],
                latitude: [
                    {
                        required: true,
                        validator: restaurantHelper.validateLatidude,
                        trigger: 'blur',
                    },
                ],
                longitude: [
                    {
                        required: true,
                        validator: restaurantHelper.validateLongitude,
                        trigger: 'blur',
                    },
                ],
                street: [{ required: true, message: 'Please input address', trigger: 'blur' }],
                /* closeTime: [
                    {
                        required: true,
                        message: 'Please input closeTime',
                        trigger: 'blur',
                    },
                ],
                openTime: [{ required: true, message: 'Please input openTime', trigger: 'blur' }], */
                tradeDiscountType: [
                    {
                        required: true,
                        message: 'Please select trade discount type',
                        trigger: 'blur',
                    },
                ],
                tradeDiscount: [
                    {
                        required: true,
                        message: 'Please input trade discount',
                        trigger: 'blur',
                        min: 0,
                        type: 'number',
                    },
                ],
                tradeDiscountPeriodType: [
                    {
                        required: true,
                        message: 'Please select trade discount period type',
                        trigger: 'blur',
                    },
                ],
                operatingStatus: [{ required: true, message: 'Chọn trạng thái', trigger: 'blur' }],
                code: [{ required: true, message: 'Nhập thiếu mã code', trigger: 'blur' }],
                approval_status: [
                    {
                        required: true,
                        message: 'Chọn trạng thái xét duyệt',
                        trigger: 'blur',
                    },
                ],
            },
        };
    },
    methods: {
        ...mapActions({
            fetchPaymentMethodList: 'paymentMethod/fetchList',
        }),
        ...mapActions('province', {
            fetchActiveProvinces: 'fetchActiveProvinces',
        }),
        ...mapActions('appSetting', {
            fetchListSetting: 'getRestaurantTradeDiscount',
        }),
        ...mapActions({
            createRestaurant: 'restaurant/createRestaurant',
            generateRestaurantDescription: 'restaurant/generateRestaurantDescription',
            generateCode: 'restaurantCounter/generateCode',
            fetchLocationList: 'location/fetchLocationList',
            fetchLocationListByParentId: 'location/fetchLocationListByParentId',
        }),
        convertProvinceIdToProvinceName(id) {
            return this.listProvince.find((province) => province.id === id)?.name;
        },
        handleGenerateDescription() {
            // check name, city, type is empty
            if (!this.form.name || !this.form.restaurant_province_id || !this.form.categories.length) {
                return this.$message.error('Tên, Thị trường, Loại cửa hàng không được trống');
            }

            const restaurantType = this.form.categories.map((category) => category.name).join(', ');
            const city = this.convertProvinceIdToProvinceName(this.form.restaurant_province_id);
            this.generateRestaurantDescription({
                restaurantName: this.form.name,
                city,
                restaurantType,
            });
        },
        onSubmit() {
            this.$refs.createRestaurantRef.validate((valid) => {
                if (valid) {
                    let { couponExp, categories, businessHours, locationId, street } = this.form;
                    let params = {
                        ...this.form,
                        location: {
                            street,
                            locationId,
                        },
                        address_note: this.form.address_note,
                        couponExp: couponExp ? couponExp.toISOString() : null,
                        // users: _.map(users, 'id'),
                        categories: _.map(categories, 'id'),
                        businessHours: businessHours.map((businessHour) => {
                            return {
                                ...businessHour,
                                workTimes: businessHour.workTimes.map(({ id, open_time, close_time }) => {
                                    return {
                                        id,
                                        open_time: open_time.format('HH:mm:ss'),
                                        close_time: close_time.format('HH:mm:ss'),
                                    };
                                }),
                            };
                        }),
                    };
                    if (!params.description) {
                        return this.$notification.error({
                            message: 'Description không được trống',
                            description: 'Description không được trống',
                        });
                    }
                    const paymentType = this.paymentType;

                    if (paymentType === 1) {
                        if (!this.paymentMethods || this.paymentMethods.length === 0) {
                            this.$notification.error({
                                message: 'Phương thức thanh toán chỉ định không được trống',
                            });
                            return;
                        }
                        params.paymentMethods = this.paymentMethods;
                    } else {
                        params.paymentMethods = null;
                    }
                    this.createRestaurant(_.omitBy(params, _.isNil));
                } else {
                    console.log('error submit!!');
                    return this.$notification.error({
                        message: 'Lỗi !!!',
                        description: 'Vui lòng kiểm tra lại dữ liệu',
                    });
                }
            });
        },
        parseDayOfWeek(day) {
            return moment().day(day).format('dddd');
        },
        onDayBusinessHourStatusChange(day, isActive) {
            let index = this.form.businessHours.findIndex((item) => item.day == day);
            if (index >= 0) this.form.businessHours[index].is_active = isActive;
        },
        addWorkTimes(day) {
            let index = this.form.businessHours.findIndex((item) => item.day == day);
            if (index >= 0)
                this.form.businessHours[index].workTimes.push({
                    open_time: moment('08:00', 'HH:mm'),
                    close_time: moment('17:00', 'HH:mm'),
                });
        },
        removeWorkTimes(day, workTimeIndex) {
            let index = this.form.businessHours.findIndex((item) => item.day == day);
            if (index >= 0) {
                if (workTimeIndex >= 0) {
                    this.form.businessHours[index].workTimes.splice(workTimeIndex, 1);
                }
            }
        },
        setDefaultBusinessHours() {
            const { openTime, closeTime } = this.defaultBusinessHours;
            if (openTime && closeTime) {
                this.form.businessHours.forEach(({ workTimes }, index) => {
                    this.form.businessHours[index].workTimes = workTimes.map(() => {
                        return {
                            open_time: moment(openTime),
                            close_time: moment(closeTime),
                        };
                    });
                });
            }
        },
        changeAddress() {
            const city = this.location.cities.find((city) => city.id === this.form.cityId);
            const district = this.location.districts.find((district) => district.id === this.form.districtId);
            const ward = this.location.wards.find((ward) => ward.id === this.form.locationId);
            const cityName = city?.name || '';
            const districtName = district?.name ? district?.name + ', ' : '';
            const wardName = ward?.name ? ward?.name + ', ' : '';
            const street = this.form.street ? this.form.street + ', ' : '';
            this.form.address = `${street}${wardName}${districtName}${cityName}`;
        },
        async onChangeCity() {
            // this.fetchLocationListByParentId(this.form.cityId);
            const districtsData = await fetchLocationListByParentId(this.form.cityId);
            this.location.districts = districtsData?.data?.data || [];
            this.form.districtId = '';
            this.form.locationId = '';
            this.location.queryCity = '';
            this.location.queryDistrict = '';
            this.location.queryWard = '';
            this.changeAddress();
        },
        async onChangeDistrict() {
            // this.fetchLocationListByParentId(this.form.districtId);
            const wardsData = await fetchLocationListByParentId(this.form.districtId);
            this.location.wards = wardsData?.data?.data || [];
            this.form.locationId = '';
            this.location.queryCity = '';
            this.location.queryDistrict = '';
            this.location.queryWard = '';
            this.changeAddress();
        },
        filterOptionLocation(input, name) {
            switch (name) {
                case this.location.ELocation.CITY:
                    this.location.queryCity = input;
                    break;
                case this.location.ELocation.DISTRICT:
                    this.location.queryDistrict = input;
                    break;
                case this.location.ELocation.WARD:
                    this.location.queryWard = input;
                    break;
                default:
                    break;
            }
        },
        mappingData(data) {
            if (data) {
                console.log(data);
                const tradeDiscountType = this.form.tradeDiscountType;
                const tradeDiscountPeriodType = this.form.tradeDiscountPeriodType;
                const tradeDiscount = data[tradeDiscountPeriodType][tradeDiscountType];
                this.form.tradeDiscount = tradeDiscount > 0 ? tradeDiscount : 10;
            }
        },
    },
    watch: {
        code: function (val, oldVal) {
            this.form.code = val;
        },
        createRestaurantSuccess: function (val, oldVal) {
            if (val && val !== oldVal) {
                this.$notification.success({
                    message: 'TẠO MỚI THÀNH CÔNG',
                    description: 'TẠO MỚI CỬA HÀNG THÀNH CÔNG',
                });
                if (this.restaurant) {
                    this.$router.push(`/restaurants/${this.restaurant.id}`);
                }
            }
        },
        createRestaurantError: function (val, oldVal) {
            if (val && val !== oldVal) {
                this.$notification.error({
                    message: 'TẠO MỚI THẤT BẠI',
                    description: this.createRestaurantMsg,
                });
            }
        },
        locationList: function (val) {
            this.location.cities = val;
        },
        RESTAURANT_TRADE_DISCOUNT_DEFAULT: function (val) {
            this.mappingData(val);
        },

        // childLocationList: function (val) {
        //     if (val && val[0].level == 2) {
        //         this.location.districts = _.clone(
        //             val.filter((location) => location.level == 2)
        //         );
        //     } else if (val && val[0].level == 3) {
        //         this.location.wards = _.clone(
        //             val.filter((location) => location.level == 3)
        //         );
        //     }
        // },

        descriptionSuccess: function (val) {
            this.form.description = this.description;
        },
        descriptionError: function (val) {
            this.$notification.error({
                message: 'Tạo mô tả thất bại',
                description: this.descriptionMsg,
            });
        },
        // watch form.tradeDiscountPeriodType & form.tradeDiscountType
        'form.tradeDiscountPeriodType': function (val) {
            this.mappingData(this.RESTAURANT_TRADE_DISCOUNT_DEFAULT);
        },
        'form.tradeDiscountType': function (val) {
            this.mappingData(this.RESTAURANT_TRADE_DISCOUNT_DEFAULT);
        },
    },
};
</script>
